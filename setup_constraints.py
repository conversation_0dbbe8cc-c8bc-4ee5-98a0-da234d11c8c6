#!/usr/bin/env python3
"""
Neo4j约束设置脚本
根据labels.csv中的标签自动创建唯一性约束和存在性约束
"""

import os
import pandas as pd
from neo4j import GraphDatabase
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def get_neo4j_driver():
    """创建Neo4j驱动连接"""
    uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    user = os.getenv("NEO4J_USER", "neo4j")
    password = os.getenv("NEO4J_PASSWORD")
    
    if not password:
        raise ValueError("NEO4J_PASSWORD环境变量未设置")
    
    return GraphDatabase.driver(uri, auth=(user, password))

def load_labels():
    """从labels.csv加载标签列表"""
    try:
        df = pd.read_csv("whitelists/labels.csv")
        labels = df["label"].tolist()
        print(f"加载了 {len(labels)} 个标签: {labels}")
        return labels
    except Exception as e:
        print(f"加载标签文件失败: {e}")
        return []

def create_constraints(driver, labels):
    """为每个标签创建约束"""
    with driver.session() as session:
        for label in labels:
            try:
                # 创建唯一性约束
                unique_constraint_name = f"{label.lower()}_name_unique"
                unique_query = f"""
                CREATE CONSTRAINT {unique_constraint_name} IF NOT EXISTS
                FOR (n:{label}) REQUIRE n.name IS UNIQUE
                """
                session.run(unique_query)
                print(f"✓ 创建唯一性约束: {unique_constraint_name}")
                
                # 创建存在性约束
                exists_constraint_name = f"{label.lower()}_desc_required"
                exists_query = f"""
                CREATE CONSTRAINT {exists_constraint_name} IF NOT EXISTS
                FOR (n:{label}) REQUIRE n.description IS NOT NULL
                """
                session.run(exists_query)
                print(f"✓ 创建存在性约束: {exists_constraint_name}")
                
            except Exception as e:
                print(f"✗ 创建约束失败 {label}: {e}")

def show_constraints(driver):
    """显示所有约束"""
    with driver.session() as session:
        try:
            result = session.run("SHOW CONSTRAINTS")
            constraints = list(result)
            print(f"\n当前数据库中的约束 ({len(constraints)} 个):")
            for constraint in constraints:
                print(f"  - {constraint['name']}: {constraint['description']}")
        except Exception as e:
            print(f"显示约束失败: {e}")

def main():
    """主函数"""
    print("开始设置Neo4j约束...")
    
    # 加载标签
    labels = load_labels()
    if not labels:
        print("没有找到标签，退出")
        return
    
    # 连接数据库
    try:
        driver = get_neo4j_driver()
        print("✓ 成功连接到Neo4j数据库")
        
        # 创建约束
        create_constraints(driver, labels)
        
        # 显示约束
        show_constraints(driver)
        
        driver.close()
        print("\n✓ 约束设置完成！")
        
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")

if __name__ == "__main__":
    main()
