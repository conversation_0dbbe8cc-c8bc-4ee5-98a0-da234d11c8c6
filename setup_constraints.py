#!/usr/bin/env python3
"""
Neo4j约束设置脚本
根据labels.csv中的标签自动创建唯一性约束和存在性约束
"""

import os
import pandas as pd
from neo4j import GraphDatabase
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def get_neo4j_driver():
    """创建Neo4j驱动连接"""
    uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    user = os.getenv("NEO4J_USER", "neo4j")
    password = os.getenv("NEO4J_PASSWORD")
    
    if not password:
        raise ValueError("NEO4J_PASSWORD环境变量未设置")
    
    return GraphDatabase.driver(uri, auth=(user, password))

def load_labels():
    """从labels.csv加载标签列表"""
    try:
        df = pd.read_csv("whitelists/labels.csv")
        labels = df["label"].tolist()
        print(f"加载了 {len(labels)} 个标签: {labels}")
        return labels
    except Exception as e:
        print(f"加载标签文件失败: {e}")
        return []

def create_constraints(driver, labels):
    """为每个标签创建约束"""
    with driver.session() as session:
        for label in labels:
            try:
                # 创建唯一性约束 - 确保每个标签下的name字段唯一
                unique_constraint_name = f"{label.lower()}_name_unique"
                unique_query = f"""
                CREATE CONSTRAINT {unique_constraint_name} IF NOT EXISTS
                FOR (n:{label}) REQUIRE n.name IS UNIQUE
                """
                session.run(unique_query)
                print(f"✓ 创建唯一性约束: {unique_constraint_name}")

                # 创建存在性约束 - 确保每个节点都有description属性
                exists_constraint_name = f"{label.lower()}_desc_required"
                exists_query = f"""
                CREATE CONSTRAINT {exists_constraint_name} IF NOT EXISTS
                FOR (n:{label}) REQUIRE n.description IS NOT NULL
                """
                session.run(exists_query)
                print(f"✓ 创建存在性约束: {exists_constraint_name}")

            except Exception as e:
                print(f"✗ 创建约束失败 {label}: {e}")

def load_whitelists():
    """加载所有白名单"""
    try:
        # 实体白名单（无header）
        entities_df = pd.read_csv("whitelists/entities.csv", header=None)
        entities = set(entities_df[0].dropna().str.strip())

        # 标签白名单
        labels_df = pd.read_csv("whitelists/labels.csv")
        labels = set(labels_df["label"].dropna().str.strip())

        # 关系白名单
        relations_df = pd.read_csv("whitelists/relations.csv")
        relations = set(relations_df["relation_type"].dropna().str.strip())

        print(f"加载白名单 - 实体: {len(entities)}, 标签: {len(labels)}, 关系: {len(relations)}")
        return entities, labels, relations

    except Exception as e:
        print(f"加载白名单失败: {e}")
        return set(), set(), set()

def clean_database_with_whitelist(driver, entities, labels, relations):
    """使用APOC清理数据库，只保留白名单中的数据"""
    with driver.session() as session:
        try:
            print("\n开始清理数据库...")

            # 1. 删除不在实体白名单中的节点（使用APOC批量处理）
            entity_list = list(entities)
            delete_nodes_query = """
            CALL apoc.periodic.iterate(
                'MATCH (n) WHERE NOT n.name IN $entities RETURN n',
                'DETACH DELETE n',
                {batchSize: 1000, params: {entities: $entities}}
            )
            """
            result = session.run(delete_nodes_query, entities=entity_list)
            node_result = result.single()
            print(f"✓ 删除了 {node_result['batches']} 批次的无效节点")

            # 2. 删除不在标签白名单中的节点
            label_list = list(labels)
            delete_invalid_labels_query = """
            CALL apoc.periodic.iterate(
                'MATCH (n) WHERE NONE(label IN labels(n) WHERE label IN $labels) RETURN n',
                'DETACH DELETE n',
                {batchSize: 1000, params: {labels: $labels}}
            )
            """
            result = session.run(delete_invalid_labels_query, labels=label_list)
            label_result = result.single()
            print(f"✓ 删除了 {label_result['batches']} 批次的无效标签节点")

            # 3. 删除不在关系白名单中的关系
            relation_list = list(relations)
            delete_invalid_relations_query = """
            CALL apoc.periodic.iterate(
                'MATCH ()-[r]-() WHERE NOT type(r) IN $relations RETURN r',
                'DELETE r',
                {batchSize: 1000, params: {relations: $relations}}
            )
            """
            result = session.run(delete_invalid_relations_query, relations=relation_list)
            rel_result = result.single()
            print(f"✓ 删除了 {rel_result['batches']} 批次的无效关系")

            # 4. 检查剩余数据
            count_query = """
            MATCH (n)
            OPTIONAL MATCH (n)-[r]-()
            RETURN count(DISTINCT n) as nodes, count(DISTINCT r) as relationships
            """
            result = session.run(count_query)
            stats = result.single()
            print(f"✓ 清理完成 - 剩余节点: {stats['nodes']}, 剩余关系: {stats['relationships']}")

        except Exception as e:
            print(f"✗ 数据库清理失败: {e}")

def show_constraints(driver):
    """显示所有约束"""
    with driver.session() as session:
        try:
            result = session.run("SHOW CONSTRAINTS")
            constraints = list(result)
            print(f"\n当前数据库中的约束 ({len(constraints)} 个):")
            for constraint in constraints:
                print(f"  - {constraint['name']}: {constraint['description']}")
        except Exception as e:
            print(f"显示约束失败: {e}")

def main():
    """主函数"""
    print("开始设置Neo4j约束和清理数据库...")

    # 加载标签
    labels = load_labels()
    if not labels:
        print("没有找到标签，退出")
        return

    # 加载所有白名单
    entities, label_whitelist, relations = load_whitelists()
    if not entities or not label_whitelist or not relations:
        print("白名单加载失败，退出")
        return

    # 连接数据库
    try:
        driver = get_neo4j_driver()
        print("✓ 成功连接到Neo4j数据库")

        # 创建约束
        create_constraints(driver, labels)

        # 显示约束
        show_constraints(driver)

        # 清理数据库（可选，如果数据库中已有数据）
        response = input("\n是否要清理数据库中不符合白名单的数据？(y/N): ")
        if response.lower() == 'y':
            clean_database_with_whitelist(driver, entities, label_whitelist, relations)

        driver.close()
        print("\n✓ 设置完成！")

    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")

if __name__ == "__main__":
    main()
