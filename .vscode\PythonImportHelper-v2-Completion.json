[{"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "importlib", "kind": 6, "isExtraImport": true, "importPath": "importlib", "description": "importlib", "detail": "importlib", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "pandas", "kind": 6, "isExtraImport": true, "importPath": "pandas", "description": "pandas", "detail": "pandas", "documentation": {}}, {"label": "get_neo4j_driver", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "batch_write_to_neo4j", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "yield_chunks", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "load_whitelists", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "llm_extract", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "whitelist_filter", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "yield_chunks", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "load_whitelists", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "llm_extract", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "whitelist_filter", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "get_neo4j_driver", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "batch_write_to_neo4j", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "load_whitelists", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "llm_extract", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "whitelist_filter", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "get_neo4j_driver", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "batch_write_to_neo4j", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "GraphDatabase", "importPath": "neo4j", "description": "neo4j", "isExtraImport": true, "detail": "neo4j", "documentation": {}}, {"label": "GraphDatabase", "importPath": "neo4j", "description": "neo4j", "isExtraImport": true, "detail": "neo4j", "documentation": {}}, {"label": "GraphDatabase", "importPath": "neo4j", "description": "neo4j", "isExtraImport": true, "detail": "neo4j", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Generator", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "clear_environment_cache", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def clear_environment_cache():\n    \"\"\"清除环境变量缓存\"\"\"\n    print(\"🧹 清除环境变量缓存...\")\n    # 清除相关的环境变量\n    env_vars = ['NEO4J_URI', 'NEO4J_USER', 'NEO4J_PASSWORD', 'NEO4J_DATABASE', 'OPENAI_API_KEY']\n    for var in env_vars:\n        if var in os.environ:\n            del os.environ[var]\n            print(f\"✓ 清除 {var}\")\ndef clear_module_cache():", "detail": "clear_cache", "documentation": {}}, {"label": "clear_module_cache", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def clear_module_cache():\n    \"\"\"清除Python模块缓存\"\"\"\n    print(\"\\n🧹 清除Python模块缓存...\")\n    modules_to_clear = ['config', 'dotenv']\n    for module_name in modules_to_clear:\n        if module_name in sys.modules:\n            del sys.modules[module_name]\n            print(f\"✓ 清除模块 {module_name}\")\ndef reload_config():\n    \"\"\"重新加载配置\"\"\"", "detail": "clear_cache", "documentation": {}}, {"label": "reload_config", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def reload_config():\n    \"\"\"重新加载配置\"\"\"\n    print(\"\\n🔄 重新加载配置...\")\n    try:\n        # 重新导入dotenv和config\n        from dotenv import load_dotenv\n        load_dotenv(override=True)  # 强制重新加载\n        from config import Config\n        Config.validate()\n        print(\"✓ 配置重新加载成功\")", "detail": "clear_cache", "documentation": {}}, {"label": "test_fresh_connection", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def test_fresh_connection():\n    \"\"\"使用新配置测试连接\"\"\"\n    print(\"\\n🔍 使用新配置测试连接...\")\n    try:\n        from neo4j import GraphDatabase\n        from config import Config\n        print(f\"URI: {Config.NEO4J_URI}\")\n        print(f\"用户: {Config.NEO4J_USER}\")\n        print(f\"密码: {'*' * len(Config.NEO4J_PASSWORD) if Config.NEO4J_PASSWORD else 'None'}\")\n        driver = GraphDatabase.driver(", "detail": "clear_cache", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"=\" * 60)\n    print(\"🧹 清除密码缓存并重新测试连接\")\n    print(\"=\" * 60)\n    # 清除缓存\n    clear_environment_cache()\n    clear_module_cache()\n    # 重新加载配置\n    config_ok = reload_config()", "detail": "clear_cache", "documentation": {}}, {"label": "Config", "kind": 6, "importPath": "config", "description": "config", "peekOfCode": "class Config:\n    \"\"\"配置类\"\"\"\n    # Neo4j 配置\n    NEO4J_URI = os.getenv(\"NEO4J_URI\", \"neo4j://127.0.0.1:7687\")\n    NEO4J_USER = os.getenv(\"NEO4J_USER\", \"neo4j\")\n    NEO4J_PASSWORD = os.getenv(\"NEO4J_PASSWORD\")\n    NEO4J_DATABASE = os.getenv(\"NEO4J_DATABASE\", \"neo4j\")\n    # OpenAI 配置\n    OPENAI_API_KEY = os.getenv(\"OPENAI_API_KEY\")\n    # 实例配置", "detail": "config", "documentation": {}}, {"label": "import_csv_to_neo4j", "kind": 2, "importPath": "csv_to_neo4j", "description": "csv_to_neo4j", "peekOfCode": "def import_csv_to_neo4j(csv_file: str, clear_db: bool = False):\n    \"\"\"\n    将CSV文件导入到Neo4j\n    Args:\n        csv_file: CSV文件路径\n        clear_db: 是否先清空数据库\n    \"\"\"\n    print(f\"=== 导入CSV到Neo4j ===\")\n    print(f\"CSV文件: {csv_file}\")\n    # 检查文件", "detail": "csv_to_neo4j", "documentation": {}}, {"label": "verify_database", "kind": 2, "importPath": "csv_to_neo4j", "description": "csv_to_neo4j", "peekOfCode": "def verify_database():\n    \"\"\"验证数据库状态\"\"\"\n    try:\n        driver = get_neo4j_driver()\n        with driver.session() as session:\n            print(\"=== 数据库验证 ===\")\n            # 基本统计\n            stats_query = \"\"\"\n            MATCH (n) \n            OPTIONAL MATCH (n)-[r]-()", "detail": "csv_to_neo4j", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "csv_to_neo4j", "description": "csv_to_neo4j", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    if len(sys.argv) < 2:\n        print(\"用法:\")\n        print(\"  python csv_to_neo4j.py <CSV文件> [--clear]\")\n        print(\"  python csv_to_neo4j.py verify\")\n        print(\"\\n示例:\")\n        print(\"  python csv_to_neo4j.py extracted_triples.csv\")\n        print(\"  python csv_to_neo4j.py extracted_triples.csv --clear\")\n        print(\"  python csv_to_neo4j.py verify\")", "detail": "csv_to_neo4j", "documentation": {}}, {"label": "extract_to_csv", "kind": 2, "importPath": "extract_to_csv", "description": "extract_to_csv", "peekOfCode": "def extract_to_csv(input_file: str, output_csv: str = \"extracted_triples.csv\"):\n    \"\"\"\n    从文本文件抽取实体关系并保存到CSV\n    Args:\n        input_file: 输入文本文件\n        output_csv: 输出CSV文件\n    \"\"\"\n    print(f\"=== 抽取实体关系到CSV ===\")\n    print(f\"输入文件: {input_file}\")\n    print(f\"输出文件: {output_csv}\")", "detail": "extract_to_csv", "documentation": {}}, {"label": "analyze_csv", "kind": 2, "importPath": "extract_to_csv", "description": "extract_to_csv", "peekOfCode": "def analyze_csv(csv_file: str):\n    \"\"\"分析CSV文件中的数据\"\"\"\n    try:\n        df = pd.read_csv(csv_file)\n        print(f\"=== 分析 {csv_file} ===\")\n        print(f\"总三元组数: {len(df)}\")\n        # 按标签统计\n        print(f\"\\n头实体标签分布:\")\n        head_labels = df['head_label'].value_counts()\n        for label, count in head_labels.items():", "detail": "extract_to_csv", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "extract_to_csv", "description": "extract_to_csv", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    if len(sys.argv) < 2:\n        print(\"用法:\")\n        print(\"  python extract_to_csv.py <输入文件> [输出文件]\")\n        print(\"  python extract_to_csv.py analyze <CSV文件>\")\n        print(\"\\n示例:\")\n        print(\"  python extract_to_csv.py python第八章.txt\")\n        print(\"  python extract_to_csv.py python第八章.txt my_output.csv\")\n        print(\"  python extract_to_csv.py analyze extracted_triples.csv\")", "detail": "extract_to_csv", "documentation": {}}, {"label": "process_text_file", "kind": 2, "importPath": "pipeline", "description": "pipeline", "peekOfCode": "def process_text_file(file_path: str, output_csv: str = None):\n    \"\"\"\n    处理文本文件，抽取实体关系并写入Neo4j\n    Args:\n        file_path: 输入文本文件路径\n        output_csv: 可选的CSV输出文件路径（用于调试）\n    \"\"\"\n    print(f\"开始处理文件: {file_path}\")\n    # 检查文件是否存在\n    if not os.path.exists(file_path):", "detail": "pipeline", "documentation": {}}, {"label": "verify_database", "kind": 2, "importPath": "pipeline", "description": "pipeline", "peekOfCode": "def verify_database():\n    \"\"\"验证数据库中的数据\"\"\"\n    try:\n        driver = get_neo4j_driver()\n        with driver.session() as session:\n            # 统计节点和关系\n            stats_query = \"\"\"\n            MATCH (n) \n            OPTIONAL MATCH (n)-[r]-()\n            RETURN count(DISTINCT n) as nodes, count(DISTINCT r) as relationships", "detail": "pipeline", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "pipeline", "description": "pipeline", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"=== Neo4j知识图谱构建管道 ===\")\n    # 检查环境变量\n    required_vars = [\"NEO4J_PASSWORD\", \"OPENAI_API_KEY\"]\n    missing_vars = [var for var in required_vars if not os.getenv(var)]\n    if missing_vars:\n        print(f\"错误: 缺少环境变量 {missing_vars}\")\n        print(\"请检查.env文件\")\n        return", "detail": "pipeline", "documentation": {}}, {"label": "run_command", "kind": 2, "importPath": "run_tests", "description": "run_tests", "peekOfCode": "def run_command(cmd, description):\n    \"\"\"运行命令并显示结果\"\"\"\n    print(f\"\\n{'='*50}\")\n    print(f\"测试: {description}\")\n    print(f\"命令: {cmd}\")\n    print('='*50)\n    try:\n        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)\n        if result.stdout:\n            print(\"输出:\")", "detail": "run_tests", "documentation": {}}, {"label": "check_prerequisites", "kind": 2, "importPath": "run_tests", "description": "run_tests", "peekOfCode": "def check_prerequisites():\n    \"\"\"检查前置条件\"\"\"\n    print(\"检查前置条件...\")\n    # 检查环境变量\n    required_vars = [\"OPENAI_API_KEY\", \"NEO4J_PASSWORD\"]\n    missing_vars = []\n    for var in required_vars:\n        if not os.getenv(var):\n            missing_vars.append(var)\n    if missing_vars:", "detail": "run_tests", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "run_tests", "description": "run_tests", "peekOfCode": "def main():\n    \"\"\"主测试流程\"\"\"\n    print(\"=== Neo4j知识图谱系统综合测试 ===\")\n    # 检查前置条件\n    if not check_prerequisites():\n        print(\"前置条件检查失败，退出测试\")\n        return\n    test_results = []\n    # 测试1: 工具函数测试\n    success = run_command(\"python utils.py\", \"工具函数测试\")", "detail": "run_tests", "documentation": {}}, {"label": "get_neo4j_driver", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def get_neo4j_driver():\n    \"\"\"创建Neo4j驱动连接\"\"\"\n    # 重新加载环境变量\n    load_dotenv(override=True)\n    # 强制使用正确的URI\n    uri = \"bolt://localhost:7687\"\n    user = os.getenv(\"NEO4J_USER\", \"neo4j\")\n    password = os.getenv(\"NEO4J_PASSWORD\")\n    if not password:\n        raise ValueError(\"NEO4J_PASSWORD环境变量未设置\")", "detail": "setup_constraints", "documentation": {}}, {"label": "load_labels", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def load_labels():\n    \"\"\"从labels.csv加载标签列表\"\"\"\n    try:\n        df = pd.read_csv(\"whitelists/labels.csv\")\n        labels = df[\"label\"].tolist()\n        print(f\"加载了 {len(labels)} 个标签: {labels}\")\n        return labels\n    except Exception as e:\n        print(f\"加载标签文件失败: {e}\")\n        return []", "detail": "setup_constraints", "documentation": {}}, {"label": "create_essential_constraints", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def create_essential_constraints(driver, labels):\n    \"\"\"为每个标签创建必要的约束\"\"\"\n    with driver.session() as session:\n        for label in labels:\n            try:\n                # 创建存在性约束 - 确保每个节点都有name属性\n                name_constraint = f\"{label.lower()}_name_required\"\n                name_query = f\"\"\"\n                CREATE CONSTRAINT {name_constraint} IF NOT EXISTS\n                FOR (n:{label}) REQUIRE n.name IS NOT NULL", "detail": "setup_constraints", "documentation": {}}, {"label": "create_label_specific_constraints", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def create_label_specific_constraints(driver, labels):\n    \"\"\"为特定标签创建约束（可选）\"\"\"\n    with driver.session() as session:\n        # 只为主要标签创建唯一性约束\n        important_labels = [\"Function\", \"Parameter\", \"CodeBlock\", \"Concept\"]\n        for label in labels:\n            if label in important_labels:\n                try:\n                    # 创建唯一性约束\n                    constraint_name = f\"{label.lower()}_name_unique\"", "detail": "setup_constraints", "documentation": {}}, {"label": "load_whitelists", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def load_whitelists():\n    \"\"\"加载所有白名单\"\"\"\n    try:\n        # 实体白名单（无header）\n        entities_df = pd.read_csv(\"whitelists/entities.csv\", header=None)\n        entities = set(entities_df[0].dropna().str.strip())\n        # 标签白名单\n        labels_df = pd.read_csv(\"whitelists/labels.csv\")\n        labels = set(labels_df[\"label\"].dropna().str.strip())\n        # 关系白名单", "detail": "setup_constraints", "documentation": {}}, {"label": "clean_database_with_whitelist", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def clean_database_with_whitelist(driver, entities, labels, relations):\n    \"\"\"使用APOC清理数据库，只保留白名单中的数据\"\"\"\n    with driver.session() as session:\n        try:\n            print(\"\\n开始清理数据库...\")\n            # 1. 删除不在实体白名单中的节点（使用APOC批量处理）\n            entity_list = list(entities)\n            delete_nodes_query = \"\"\"\n            CALL apoc.periodic.iterate(\n                'MATCH (n) WHERE NOT n.name IN $entities RETURN n',", "detail": "setup_constraints", "documentation": {}}, {"label": "show_constraints", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def show_constraints(driver):\n    \"\"\"显示所有约束\"\"\"\n    with driver.session() as session:\n        try:\n            result = session.run(\"SHOW CONSTRAINTS\")\n            constraints = list(result)\n            print(f\"\\n当前数据库中的约束 ({len(constraints)} 个):\")\n            for constraint in constraints:\n                print(f\"  - {constraint['name']}: {constraint['description']}\")\n        except Exception as e:", "detail": "setup_constraints", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"开始设置Neo4j约束和清理数据库...\")\n    # 加载标签\n    labels = load_labels()\n    if not labels:\n        print(\"没有找到标签，退出\")\n        return\n    # 加载所有白名单\n    entities, label_whitelist, relations = load_whitelists()", "detail": "setup_constraints", "documentation": {}}, {"label": "test_connection_detailed", "kind": 2, "importPath": "test_neo4j_connection", "description": "test_neo4j_connection", "peekOfCode": "def test_connection_detailed():\n    \"\"\"详细测试Neo4j连接\"\"\"\n    print(\"=== Neo4j连接详细测试 ===\")\n    # 重新加载环境变量\n    load_dotenv(override=True)\n    # 1. 显示环境变量\n    print(\"\\n1. 环境变量检查:\")\n    uri = os.getenv(\"NEO4J_URI\", \"bolt://localhost:7687\")\n    user = os.getenv(\"NEO4J_USER\", \"neo4j\")\n    password = os.getenv(\"NEO4J_PASSWORD\")", "detail": "test_neo4j_connection", "documentation": {}}, {"label": "test_different_configs", "kind": 2, "importPath": "test_neo4j_connection", "description": "test_neo4j_connection", "peekOfCode": "def test_different_configs():\n    \"\"\"测试不同的连接配置\"\"\"\n    print(\"\\n=== 测试不同连接配置 ===\")\n    configs = [\n        {\n            \"name\": \"默认配置\",\n            \"uri\": \"bolt://localhost:7687\",\n            \"user\": \"neo4j\",\n            \"password\": os.getenv(\"NEO4J_PASSWORD\")\n        },", "detail": "test_neo4j_connection", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_neo4j_connection", "description": "test_neo4j_connection", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"Neo4j连接诊断工具\")\n    print(\"=\" * 50)\n    # 主要连接测试\n    success = test_connection_detailed()\n    if not success:\n        print(\"\\n主要连接测试失败，尝试其他配置...\")\n        test_different_configs()\n    print(f\"\\n{'='*50}\")", "detail": "test_neo4j_connection", "documentation": {}}, {"label": "import_csv_data", "kind": 2, "importPath": "test_pipeline", "description": "test_pipeline", "peekOfCode": "def import_csv_data():\n    \"\"\"直接导入CSV数据到Neo4j\"\"\"\n    print(\"=== 导入CSV数据到Neo4j ===\")\n    # 读取CSV文件\n    try:\n        import pandas as pd\n        df = pd.read_csv(\"extracted_triples.csv\")\n        print(f\"读取到 {len(df)} 个三元组\")\n    except Exception as e:\n        print(f\"读取CSV失败: {e}\")", "detail": "test_pipeline", "documentation": {}}, {"label": "test_small_pipeline", "kind": 2, "importPath": "test_pipeline", "description": "test_pipeline", "peekOfCode": "def test_small_pipeline():\n    \"\"\"测试小量数据的完整流程\"\"\"\n    print(\"=== 测试知识图谱构建管道 ===\")\n    # 测试文本\n    test_text = \"\"\"\n    ## 8.1 定义函数\n    函数是带名字的代码块，用于完成具体的工作。要执行函数定义的特定任务，可调用该函数。\n    当需要在程序中多次执行同一项任务时，无须反复编写完成该任务的代码，只需要调用执行该任务的函数。\n    函数可以接受参数，参数是传递给函数的值。函数还可以返回值给调用者。\n    实参是调用函数时传递的实际值，形参是函数定义中的参数名。", "detail": "test_pipeline", "documentation": {}}, {"label": "clear_test_data", "kind": 2, "importPath": "test_pipeline", "description": "test_pipeline", "peekOfCode": "def clear_test_data():\n    \"\"\"清理测试数据\"\"\"\n    try:\n        driver = get_neo4j_driver()\n        with driver.session() as session:\n            # 删除所有节点和关系\n            session.run(\"MATCH (n) DETACH DELETE n\")\n            print(\"✓ 测试数据已清理\")\n        driver.close()\n    except Exception as e:", "detail": "test_pipeline", "documentation": {}}, {"label": "yield_chunks", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def yield_chunks(text: str, max_chars: int = 2800) -> Generator[str, None, None]:\n    \"\"\"\n    将文本分割成适合GPT处理的小块\n    Args:\n        text: 输入文本\n        max_chars: 每块最大字符数（约700 tokens）\n    Yields:\n        str: 文本块\n    \"\"\"\n    buffer = []", "detail": "utils", "documentation": {}}, {"label": "load_whitelists", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def load_whitelists() -> tuple[set, set, set]:\n    \"\"\"\n    加载白名单文件\n    Returns:\n        tuple: (实体白名单, 标签白名单, 关系白名单)\n    \"\"\"\n    try:\n        # 实体白名单（无header）\n        entities_df = pd.read_csv(\"whitelists/entities.csv\", header=None)\n        entity_whitelist = set(entities_df[0].dropna().str.strip())", "detail": "utils", "documentation": {}}, {"label": "get_openai_client", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def get_openai_client() -> OpenAI:\n    \"\"\"\n    创建OpenAI客户端\n    Returns:\n        OpenAI: 客户端实例\n    \"\"\"\n    api_key = os.getenv(\"OPENAI_API_KEY\")\n    if not api_key:\n        raise ValueError(\"OPENAI_API_KEY环境变量未设置\")\n    return OpenAI(api_key=api_key)", "detail": "utils", "documentation": {}}, {"label": "create_extraction_function_def", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def create_extraction_function_def() -> Dict[str, Any]:\n    \"\"\"\n    创建GPT函数调用定义\n    Returns:\n        dict: 函数定义\n    \"\"\"\n    return {\n        \"name\": \"add_triples\",\n        \"description\": \"提取并返回实体关系三元组\",\n        \"parameters\": {", "detail": "utils", "documentation": {}}, {"label": "create_system_prompt", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def create_system_prompt(entity_whitelist: set, label_whitelist: set, relation_whitelist: set) -> str:\n    \"\"\"\n    创建系统提示词\n    Args:\n        entity_whitelist: 实体白名单\n        label_whitelist: 标签白名单\n        relation_whitelist: 关系白名单\n    Returns:\n        str: 系统提示词\n    \"\"\"", "detail": "utils", "documentation": {}}, {"label": "llm_extract", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def llm_extract(chunk: str, entity_whitelist: set, label_whitelist: set, relation_whitelist: set) -> List[Dict[str, str]]:\n    \"\"\"\n    使用GPT-4o抽取实体关系三元组\n    Args:\n        chunk: 文本块\n        entity_whitelist: 实体白名单\n        label_whitelist: 标签白名单\n        relation_whitelist: 关系白名单\n    Returns:\n        List[Dict]: 抽取的三元组列表", "detail": "utils", "documentation": {}}, {"label": "whitelist_filter", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def whitelist_filter(triples: List[Dict[str, str]], entity_whitelist: set, label_whitelist: set, relation_whitelist: set) -> Generator[Dict[str, str], None, None]:\n    \"\"\"\n    根据白名单过滤三元组\n    Args:\n        triples: 三元组列表\n        entity_whitelist: 实体白名单\n        label_whitelist: 标签白名单\n        relation_whitelist: 关系白名单\n    Yields:\n        Dict: 过滤后的三元组", "detail": "utils", "documentation": {}}, {"label": "get_neo4j_driver", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def get_neo4j_driver():\n    \"\"\"创建Neo4j驱动连接\"\"\"\n    # 重新加载环境变量\n    load_dotenv(override=True)\n    # 强制使用正确的URI\n    uri = \"bolt://localhost:7687\"\n    user = os.getenv(\"NEO4J_USER\", \"neo4j\")\n    password = os.getenv(\"NEO4J_PASSWORD\")\n    if not password:\n        raise ValueError(\"NEO4J_PASSWORD环境变量未设置\")", "detail": "utils", "documentation": {}}, {"label": "batch_write_to_neo4j", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def batch_write_to_neo4j(driver, triples_df: pd.DataFrame):\n    \"\"\"\n    批量写入三元组到Neo4j\n    Args:\n        driver: Neo4j驱动\n        triples_df: 包含三元组的DataFrame\n    \"\"\"\n    if triples_df.empty:\n        print(\"没有数据需要写入\")\n        return", "detail": "utils", "documentation": {}}, {"label": "test_neo4j_connection", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def test_neo4j_connection():\n    \"\"\"测试Neo4j连接\"\"\"\n    try:\n        driver = get_neo4j_driver()\n        with driver.session() as session:\n            result = session.run(\"RETURN 'Hello Neo4j!' as message\")\n            message = result.single()[\"message\"]\n            print(f\"✓ Neo4j连接成功: {message}\")\n        driver.close()\n        return True", "detail": "utils", "documentation": {}}, {"label": "test_text_chunking", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def test_text_chunking():\n    \"\"\"测试文本分块功能\"\"\"\n    print(\"测试文本分块功能...\")\n    # 读取测试文件\n    try:\n        with open(\"python第八章.txt\", \"r\", encoding=\"utf-8\") as f:\n            text = f.read()\n        print(f\"原文本长度: {len(text)} 字符\")\n        chunks = list(yield_chunks(text))\n        print(f\"分块数量: {len(chunks)}\")", "detail": "utils", "documentation": {}}, {"label": "test_llm_extraction", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def test_llm_extraction():\n    \"\"\"测试LLM抽取功能\"\"\"\n    print(\"\\n测试LLM抽取功能...\")\n    # 加载白名单\n    entities, labels, relations = load_whitelists()\n    # 测试文本\n    test_text = \"\"\"\n    函数是带名字的代码块，用于完成具体的工作。要执行函数定义的特定任务，可调用该函数。\n    函数可以接受参数，参数是传递给函数的值。函数还可以返回值给调用者。\n    \"\"\"", "detail": "utils", "documentation": {}}]