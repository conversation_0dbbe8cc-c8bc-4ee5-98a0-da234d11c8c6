[{"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "importlib", "kind": 6, "isExtraImport": true, "importPath": "importlib", "description": "importlib", "detail": "importlib", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "pandas", "kind": 6, "isExtraImport": true, "importPath": "pandas", "description": "pandas", "detail": "pandas", "documentation": {}}, {"label": "GraphDatabase", "importPath": "neo4j", "description": "neo4j", "isExtraImport": true, "detail": "neo4j", "documentation": {}}, {"label": "GraphDatabase", "importPath": "neo4j", "description": "neo4j", "isExtraImport": true, "detail": "neo4j", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Generator", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "clear_environment_cache", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def clear_environment_cache():\n    \"\"\"清除环境变量缓存\"\"\"\n    print(\"🧹 清除环境变量缓存...\")\n    # 清除相关的环境变量\n    env_vars = ['NEO4J_URI', 'NEO4J_USER', 'NEO4J_PASSWORD', 'NEO4J_DATABASE', 'OPENAI_API_KEY']\n    for var in env_vars:\n        if var in os.environ:\n            del os.environ[var]\n            print(f\"✓ 清除 {var}\")\ndef clear_module_cache():", "detail": "clear_cache", "documentation": {}}, {"label": "clear_module_cache", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def clear_module_cache():\n    \"\"\"清除Python模块缓存\"\"\"\n    print(\"\\n🧹 清除Python模块缓存...\")\n    modules_to_clear = ['config', 'dotenv']\n    for module_name in modules_to_clear:\n        if module_name in sys.modules:\n            del sys.modules[module_name]\n            print(f\"✓ 清除模块 {module_name}\")\ndef reload_config():\n    \"\"\"重新加载配置\"\"\"", "detail": "clear_cache", "documentation": {}}, {"label": "reload_config", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def reload_config():\n    \"\"\"重新加载配置\"\"\"\n    print(\"\\n🔄 重新加载配置...\")\n    try:\n        # 重新导入dotenv和config\n        from dotenv import load_dotenv\n        load_dotenv(override=True)  # 强制重新加载\n        from config import Config\n        Config.validate()\n        print(\"✓ 配置重新加载成功\")", "detail": "clear_cache", "documentation": {}}, {"label": "test_fresh_connection", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def test_fresh_connection():\n    \"\"\"使用新配置测试连接\"\"\"\n    print(\"\\n🔍 使用新配置测试连接...\")\n    try:\n        from neo4j import GraphDatabase\n        from config import Config\n        print(f\"URI: {Config.NEO4J_URI}\")\n        print(f\"用户: {Config.NEO4J_USER}\")\n        print(f\"密码: {'*' * len(Config.NEO4J_PASSWORD) if Config.NEO4J_PASSWORD else 'None'}\")\n        driver = GraphDatabase.driver(", "detail": "clear_cache", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"=\" * 60)\n    print(\"🧹 清除密码缓存并重新测试连接\")\n    print(\"=\" * 60)\n    # 清除缓存\n    clear_environment_cache()\n    clear_module_cache()\n    # 重新加载配置\n    config_ok = reload_config()", "detail": "clear_cache", "documentation": {}}, {"label": "Config", "kind": 6, "importPath": "config", "description": "config", "peekOfCode": "class Config:\n    \"\"\"配置类\"\"\"\n    # Neo4j 配置\n    NEO4J_URI = os.getenv(\"NEO4J_URI\", \"neo4j://127.0.0.1:7687\")\n    NEO4J_USER = os.getenv(\"NEO4J_USER\", \"neo4j\")\n    NEO4J_PASSWORD = os.getenv(\"NEO4J_PASSWORD\")\n    NEO4J_DATABASE = os.getenv(\"NEO4J_DATABASE\", \"neo4j\")\n    # OpenAI 配置\n    OPENAI_API_KEY = os.getenv(\"OPENAI_API_KEY\")\n    # 实例配置", "detail": "config", "documentation": {}}, {"label": "get_neo4j_driver", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def get_neo4j_driver():\n    \"\"\"创建Neo4j驱动连接\"\"\"\n    uri = os.getenv(\"NEO4J_URI\", \"bolt://localhost:7687\")\n    user = os.getenv(\"NEO4J_USER\", \"neo4j\")\n    password = os.getenv(\"NEO4J_PASSWORD\")\n    if not password:\n        raise ValueError(\"NEO4J_PASSWORD环境变量未设置\")\n    return GraphDatabase.driver(uri, auth=(user, password))\ndef load_labels():\n    \"\"\"从labels.csv加载标签列表\"\"\"", "detail": "setup_constraints", "documentation": {}}, {"label": "load_labels", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def load_labels():\n    \"\"\"从labels.csv加载标签列表\"\"\"\n    try:\n        df = pd.read_csv(\"whitelists/labels.csv\")\n        labels = df[\"label\"].tolist()\n        print(f\"加载了 {len(labels)} 个标签: {labels}\")\n        return labels\n    except Exception as e:\n        print(f\"加载标签文件失败: {e}\")\n        return []", "detail": "setup_constraints", "documentation": {}}, {"label": "create_constraints", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def create_constraints(driver, labels):\n    \"\"\"为每个标签创建约束\"\"\"\n    with driver.session() as session:\n        for label in labels:\n            try:\n                # 创建唯一性约束 - 确保每个标签下的name字段唯一\n                unique_constraint_name = f\"{label.lower()}_name_unique\"\n                unique_query = f\"\"\"\n                CREATE CONSTRAINT {unique_constraint_name} IF NOT EXISTS\n                FOR (n:{label}) REQUIRE n.name IS UNIQUE", "detail": "setup_constraints", "documentation": {}}, {"label": "load_whitelists", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def load_whitelists():\n    \"\"\"加载所有白名单\"\"\"\n    try:\n        # 实体白名单（无header）\n        entities_df = pd.read_csv(\"whitelists/entities.csv\", header=None)\n        entities = set(entities_df[0].dropna().str.strip())\n        # 标签白名单\n        labels_df = pd.read_csv(\"whitelists/labels.csv\")\n        labels = set(labels_df[\"label\"].dropna().str.strip())\n        # 关系白名单", "detail": "setup_constraints", "documentation": {}}, {"label": "clean_database_with_whitelist", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def clean_database_with_whitelist(driver, entities, labels, relations):\n    \"\"\"使用APOC清理数据库，只保留白名单中的数据\"\"\"\n    with driver.session() as session:\n        try:\n            print(\"\\n开始清理数据库...\")\n            # 1. 删除不在实体白名单中的节点（使用APOC批量处理）\n            entity_list = list(entities)\n            delete_nodes_query = \"\"\"\n            CALL apoc.periodic.iterate(\n                'MATCH (n) WHERE NOT n.name IN $entities RETURN n',", "detail": "setup_constraints", "documentation": {}}, {"label": "show_constraints", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def show_constraints(driver):\n    \"\"\"显示所有约束\"\"\"\n    with driver.session() as session:\n        try:\n            result = session.run(\"SHOW CONSTRAINTS\")\n            constraints = list(result)\n            print(f\"\\n当前数据库中的约束 ({len(constraints)} 个):\")\n            for constraint in constraints:\n                print(f\"  - {constraint['name']}: {constraint['description']}\")\n        except Exception as e:", "detail": "setup_constraints", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"开始设置Neo4j约束和清理数据库...\")\n    # 加载标签\n    labels = load_labels()\n    if not labels:\n        print(\"没有找到标签，退出\")\n        return\n    # 加载所有白名单\n    entities, label_whitelist, relations = load_whitelists()", "detail": "setup_constraints", "documentation": {}}, {"label": "yield_chunks", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def yield_chunks(text: str, max_chars: int = 2800) -> Generator[str, None, None]:\n    \"\"\"\n    将文本分割成适合GPT处理的小块\n    Args:\n        text: 输入文本\n        max_chars: 每块最大字符数（约700 tokens）\n    Yields:\n        str: 文本块\n    \"\"\"\n    buffer = []", "detail": "utils", "documentation": {}}, {"label": "load_whitelists", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def load_whitelists() -> tuple[set, set, set]:\n    \"\"\"\n    加载白名单文件\n    Returns:\n        tuple: (实体白名单, 标签白名单, 关系白名单)\n    \"\"\"\n    try:\n        # 实体白名单（无header）\n        entities_df = pd.read_csv(\"whitelists/entities.csv\", header=None)\n        entity_whitelist = set(entities_df[0].dropna().str.strip())", "detail": "utils", "documentation": {}}, {"label": "get_openai_client", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def get_openai_client() -> OpenAI:\n    \"\"\"\n    创建OpenAI客户端\n    Returns:\n        OpenAI: 客户端实例\n    \"\"\"\n    api_key = os.getenv(\"OPENAI_API_KEY\")\n    if not api_key:\n        raise ValueError(\"OPENAI_API_KEY环境变量未设置\")\n    return OpenAI(api_key=api_key)", "detail": "utils", "documentation": {}}, {"label": "create_extraction_function_def", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def create_extraction_function_def() -> Dict[str, Any]:\n    \"\"\"\n    创建GPT函数调用定义\n    Returns:\n        dict: 函数定义\n    \"\"\"\n    return {\n        \"name\": \"add_triples\",\n        \"description\": \"提取并返回实体关系三元组\",\n        \"parameters\": {", "detail": "utils", "documentation": {}}, {"label": "create_system_prompt", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def create_system_prompt(entity_whitelist: set, label_whitelist: set, relation_whitelist: set) -> str:\n    \"\"\"\n    创建系统提示词\n    Args:\n        entity_whitelist: 实体白名单\n        label_whitelist: 标签白名单\n        relation_whitelist: 关系白名单\n    Returns:\n        str: 系统提示词\n    \"\"\"", "detail": "utils", "documentation": {}}, {"label": "llm_extract", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def llm_extract(chunk: str, entity_whitelist: set, label_whitelist: set, relation_whitelist: set) -> List[Dict[str, str]]:\n    \"\"\"\n    使用GPT-4o抽取实体关系三元组\n    Args:\n        chunk: 文本块\n        entity_whitelist: 实体白名单\n        label_whitelist: 标签白名单\n        relation_whitelist: 关系白名单\n    Returns:\n        List[Dict]: 抽取的三元组列表", "detail": "utils", "documentation": {}}, {"label": "whitelist_filter", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def whitelist_filter(triples: List[Dict[str, str]], entity_whitelist: set, label_whitelist: set, relation_whitelist: set) -> Generator[Dict[str, str], None, None]:\n    \"\"\"\n    根据白名单过滤三元组\n    Args:\n        triples: 三元组列表\n        entity_whitelist: 实体白名单\n        label_whitelist: 标签白名单\n        relation_whitelist: 关系白名单\n    Yields:\n        Dict: 过滤后的三元组", "detail": "utils", "documentation": {}}, {"label": "get_neo4j_driver", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def get_neo4j_driver():\n    \"\"\"创建Neo4j驱动连接\"\"\"\n    uri = os.getenv(\"NEO4J_URI\", \"bolt://localhost:7687\")\n    user = os.getenv(\"NEO4J_USER\", \"neo4j\")\n    password = os.getenv(\"NEO4J_PASSWORD\")\n    if not password:\n        raise ValueError(\"NEO4J_PASSWORD环境变量未设置\")\n    return GraphDatabase.driver(uri, auth=(user, password))\ndef batch_write_to_neo4j(driver, triples_df: pd.DataFrame):\n    \"\"\"", "detail": "utils", "documentation": {}}, {"label": "batch_write_to_neo4j", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def batch_write_to_neo4j(driver, triples_df: pd.DataFrame):\n    \"\"\"\n    批量写入三元组到Neo4j\n    Args:\n        driver: Neo4j驱动\n        triples_df: 包含三元组的DataFrame\n    \"\"\"\n    if triples_df.empty:\n        print(\"没有数据需要写入\")\n        return", "detail": "utils", "documentation": {}}, {"label": "test_neo4j_connection", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def test_neo4j_connection():\n    \"\"\"测试Neo4j连接\"\"\"\n    try:\n        driver = get_neo4j_driver()\n        with driver.session() as session:\n            result = session.run(\"RETURN 'Hello Neo4j!' as message\")\n            message = result.single()[\"message\"]\n            print(f\"✓ Neo4j连接成功: {message}\")\n        driver.close()\n        return True", "detail": "utils", "documentation": {}}, {"label": "test_text_chunking", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def test_text_chunking():\n    \"\"\"测试文本分块功能\"\"\"\n    print(\"测试文本分块功能...\")\n    # 读取测试文件\n    try:\n        with open(\"python第八章.txt\", \"r\", encoding=\"utf-8\") as f:\n            text = f.read()\n        print(f\"原文本长度: {len(text)} 字符\")\n        chunks = list(yield_chunks(text))\n        print(f\"分块数量: {len(chunks)}\")", "detail": "utils", "documentation": {}}, {"label": "test_llm_extraction", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def test_llm_extraction():\n    \"\"\"测试LLM抽取功能\"\"\"\n    print(\"\\n测试LLM抽取功能...\")\n    # 加载白名单\n    entities, labels, relations = load_whitelists()\n    # 测试文本\n    test_text = \"\"\"\n    函数是带名字的代码块，用于完成具体的工作。要执行函数定义的特定任务，可调用该函数。\n    函数可以接受参数，参数是传递给函数的值。函数还可以返回值给调用者。\n    \"\"\"", "detail": "utils", "documentation": {}}]