[{"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "importlib", "kind": 6, "isExtraImport": true, "importPath": "importlib", "description": "importlib", "detail": "importlib", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "pandas", "kind": 6, "isExtraImport": true, "importPath": "pandas", "description": "pandas", "detail": "pandas", "documentation": {}}, {"label": "GraphDatabase", "importPath": "neo4j", "description": "neo4j", "isExtraImport": true, "detail": "neo4j", "documentation": {}}, {"label": "clear_environment_cache", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def clear_environment_cache():\n    \"\"\"清除环境变量缓存\"\"\"\n    print(\"🧹 清除环境变量缓存...\")\n    # 清除相关的环境变量\n    env_vars = ['NEO4J_URI', 'NEO4J_USER', 'NEO4J_PASSWORD', 'NEO4J_DATABASE', 'OPENAI_API_KEY']\n    for var in env_vars:\n        if var in os.environ:\n            del os.environ[var]\n            print(f\"✓ 清除 {var}\")\ndef clear_module_cache():", "detail": "clear_cache", "documentation": {}}, {"label": "clear_module_cache", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def clear_module_cache():\n    \"\"\"清除Python模块缓存\"\"\"\n    print(\"\\n🧹 清除Python模块缓存...\")\n    modules_to_clear = ['config', 'dotenv']\n    for module_name in modules_to_clear:\n        if module_name in sys.modules:\n            del sys.modules[module_name]\n            print(f\"✓ 清除模块 {module_name}\")\ndef reload_config():\n    \"\"\"重新加载配置\"\"\"", "detail": "clear_cache", "documentation": {}}, {"label": "reload_config", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def reload_config():\n    \"\"\"重新加载配置\"\"\"\n    print(\"\\n🔄 重新加载配置...\")\n    try:\n        # 重新导入dotenv和config\n        from dotenv import load_dotenv\n        load_dotenv(override=True)  # 强制重新加载\n        from config import Config\n        Config.validate()\n        print(\"✓ 配置重新加载成功\")", "detail": "clear_cache", "documentation": {}}, {"label": "test_fresh_connection", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def test_fresh_connection():\n    \"\"\"使用新配置测试连接\"\"\"\n    print(\"\\n🔍 使用新配置测试连接...\")\n    try:\n        from neo4j import GraphDatabase\n        from config import Config\n        print(f\"URI: {Config.NEO4J_URI}\")\n        print(f\"用户: {Config.NEO4J_USER}\")\n        print(f\"密码: {'*' * len(Config.NEO4J_PASSWORD) if Config.NEO4J_PASSWORD else 'None'}\")\n        driver = GraphDatabase.driver(", "detail": "clear_cache", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"=\" * 60)\n    print(\"🧹 清除密码缓存并重新测试连接\")\n    print(\"=\" * 60)\n    # 清除缓存\n    clear_environment_cache()\n    clear_module_cache()\n    # 重新加载配置\n    config_ok = reload_config()", "detail": "clear_cache", "documentation": {}}, {"label": "Config", "kind": 6, "importPath": "config", "description": "config", "peekOfCode": "class Config:\n    \"\"\"配置类\"\"\"\n    # Neo4j 配置\n    NEO4J_URI = os.getenv(\"NEO4J_URI\", \"neo4j://127.0.0.1:7687\")\n    NEO4J_USER = os.getenv(\"NEO4J_USER\", \"neo4j\")\n    NEO4J_PASSWORD = os.getenv(\"NEO4J_PASSWORD\")\n    NEO4J_DATABASE = os.getenv(\"NEO4J_DATABASE\", \"neo4j\")\n    # OpenAI 配置\n    OPENAI_API_KEY = os.getenv(\"OPENAI_API_KEY\")\n    # 实例配置", "detail": "config", "documentation": {}}, {"label": "get_neo4j_driver", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def get_neo4j_driver():\n    \"\"\"创建Neo4j驱动连接\"\"\"\n    uri = os.getenv(\"NEO4J_URI\", \"bolt://localhost:7687\")\n    user = os.getenv(\"NEO4J_USER\", \"neo4j\")\n    password = os.getenv(\"NEO4J_PASSWORD\")\n    if not password:\n        raise ValueError(\"NEO4J_PASSWORD环境变量未设置\")\n    return GraphDatabase.driver(uri, auth=(user, password))\ndef load_labels():\n    \"\"\"从labels.csv加载标签列表\"\"\"", "detail": "setup_constraints", "documentation": {}}, {"label": "load_labels", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def load_labels():\n    \"\"\"从labels.csv加载标签列表\"\"\"\n    try:\n        df = pd.read_csv(\"whitelists/labels.csv\")\n        labels = df[\"label\"].tolist()\n        print(f\"加载了 {len(labels)} 个标签: {labels}\")\n        return labels\n    except Exception as e:\n        print(f\"加载标签文件失败: {e}\")\n        return []", "detail": "setup_constraints", "documentation": {}}, {"label": "create_constraints", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def create_constraints(driver, labels):\n    \"\"\"为每个标签创建约束\"\"\"\n    with driver.session() as session:\n        for label in labels:\n            try:\n                # 创建唯一性约束\n                unique_constraint_name = f\"{label.lower()}_name_unique\"\n                unique_query = f\"\"\"\n                CREATE CONSTRAINT {unique_constraint_name} IF NOT EXISTS\n                FOR (n:{label}) REQUIRE n.name IS UNIQUE", "detail": "setup_constraints", "documentation": {}}, {"label": "show_constraints", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def show_constraints(driver):\n    \"\"\"显示所有约束\"\"\"\n    with driver.session() as session:\n        try:\n            result = session.run(\"SHOW CONSTRAINTS\")\n            constraints = list(result)\n            print(f\"\\n当前数据库中的约束 ({len(constraints)} 个):\")\n            for constraint in constraints:\n                print(f\"  - {constraint['name']}: {constraint['description']}\")\n        except Exception as e:", "detail": "setup_constraints", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "setup_constraints", "description": "setup_constraints", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"开始设置Neo4j约束...\")\n    # 加载标签\n    labels = load_labels()\n    if not labels:\n        print(\"没有找到标签，退出\")\n        return\n    # 连接数据库\n    try:", "detail": "setup_constraints", "documentation": {}}]