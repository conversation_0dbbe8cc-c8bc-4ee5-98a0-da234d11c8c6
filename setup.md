# gyrw-kg 环境设置指南

## 📋 环境信息

- **实例名称**: gyrw-kg
- **Neo4j版本**: 2025.05.0
- **数据库用户**: neo4j
- **数据库名称**: neo4j
- **连接地址**: neo4j://127.0.0.1:7687

## 🚀 快速开始

### 1. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 2. 验证环境配置

```bash
python config.py
```

### 3. 测试连接

```bash
python test_connections.py
```

如果看到"🎉 所有连接测试通过！"，说明环境配置正确。

## 📁 文件结构

```
gyrw-kg/
├── .env                    # 环境变量配置
├── config.py              # 配置管理
├── test_connections.py     # 连接测试
├── requirements.txt        # Python依赖
├── whitelists/            # 白名单文件夹
│   ├── entities.csv       # 实体白名单
│   ├── labels.csv         # 标签白名单
│   └── relations.csv      # 关系白名单
└── neo4j-txt-to-database.md  # 详细文档
```

## ⚙️ 配置说明

### 环境变量 (.env)

- `NEO4J_URI`: Neo4j连接地址
- `NEO4J_USER`: 数据库用户名
- `NEO4J_PASSWORD`: 数据库密码
- `NEO4J_DATABASE`: 数据库名称
- `OPENAI_API_KEY`: OpenAI API密钥

### 白名单文件

1. **entities.csv**: 只包含实体名称，每行一个
2. **labels.csv**: 包含允许的标签类型
3. **relations.csv**: 包含允许的关系类型

## 🔧 故障排除

### Neo4j连接问题

1. 确认Neo4j服务正在运行
2. 检查端口7687是否开放
3. 验证用户名和密码是否正确

### OpenAI连接问题

1. 检查API密钥是否有效
2. 确认账户有足够的额度
3. 验证网络连接

## 📚 下一步

环境设置完成后，可以：

1. 准备要处理的文本文件
2. 运行主要的数据处理pipeline
3. 查看Neo4j数据库中的结果

详细使用说明请参考 `neo4j-txt-to-database.md`。
