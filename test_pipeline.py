#!/usr/bin/env python3
"""
测试管道脚本 - 用小量数据测试完整流程
"""

import os
import pandas as pd
from dotenv import load_dotenv

from utils import (
    load_whitelists, llm_extract, whitelist_filter,
    get_neo4j_driver, batch_write_to_neo4j
)

load_dotenv()

def import_csv_data():
    """直接导入CSV数据到Neo4j"""
    print("=== 导入CSV数据到Neo4j ===")

    # 读取CSV文件
    try:
        import pandas as pd
        df = pd.read_csv("extracted_triples.csv")
        print(f"读取到 {len(df)} 个三元组")
    except Exception as e:
        print(f"读取CSV失败: {e}")
        return False

    # 连接数据库并导入
    try:
        from utils import get_neo4j_driver, batch_write_to_neo4j

        # 强制使用正确的连接
        driver = get_neo4j_driver()

        # 清空数据库
        with driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")
            print("✓ 数据库已清空")

        # 批量写入
        batch_write_to_neo4j(driver, df)

        # 验证结果
        with driver.session() as session:
            result = session.run("""
                MATCH (n)
                OPTIONAL MATCH (n)-[r]-()
                RETURN count(DISTINCT n) as nodes, count(DISTINCT r) as relationships
            """)
            stats = result.single()
            print(f"✓ 导入完成 - 节点: {stats['nodes']}, 关系: {stats['relationships']}")

            # 显示示例数据
            sample_result = session.run("""
                MATCH (n)-[r]->(m)
                RETURN n.name as head, labels(n)[0] as head_label,
                       type(r) as relation,
                       m.name as tail, labels(m)[0] as tail_label,
                       n.description as head_desc, m.description as tail_desc
                LIMIT 5
            """)

            print("\n示例数据:")
            for i, record in enumerate(sample_result, 1):
                print(f"  {i}. {record['head']} ({record['head_label']}) -> {record['relation']} -> {record['tail']} ({record['tail_label']})")
                if record['head_desc']:
                    print(f"     头实体: {record['head_desc']}")
                if record['tail_desc']:
                    print(f"     尾实体: {record['tail_desc']}")

        driver.close()
        return True

    except Exception as e:
        print(f"导入失败: {e}")
        return False

def test_small_pipeline():
    """测试小量数据的完整流程"""
    print("=== 测试知识图谱构建管道 ===")

    # 测试文本
    test_text = """
    ## 8.1 定义函数

    函数是带名字的代码块，用于完成具体的工作。要执行函数定义的特定任务，可调用该函数。
    当需要在程序中多次执行同一项任务时，无须反复编写完成该任务的代码，只需要调用执行该任务的函数。

    函数可以接受参数，参数是传递给函数的值。函数还可以返回值给调用者。
    实参是调用函数时传递的实际值，形参是函数定义中的参数名。
    """
    
    print(f"测试文本长度: {len(test_text)} 字符")
    
    # 1. 加载白名单
    print("\n1. 加载白名单...")
    entities, labels, relations = load_whitelists()
    print(f"实体: {len(entities)}, 标签: {len(labels)}, 关系: {len(relations)}")
    
    # 2. LLM抽取
    print("\n2. LLM抽取实体关系...")
    try:
        triples = llm_extract(test_text, entities, labels, relations)
        print(f"抽取到 {len(triples)} 个三元组")
        
        # 显示抽取结果
        for i, triple in enumerate(triples):
            print(f"  {i+1}. {triple.get('head', 'N/A')} -> {triple.get('relation', 'N/A')} -> {triple.get('tail', 'N/A')}")
            
    except Exception as e:
        print(f"LLM抽取失败: {e}")
        return False
    
    # 3. 白名单过滤
    print("\n3. 白名单过滤...")
    filtered_triples = list(whitelist_filter(triples, entities, labels, relations))
    print(f"过滤后剩余 {len(filtered_triples)} 个三元组")
    
    if not filtered_triples:
        print("没有有效的三元组，测试结束")
        return True
    
    # 显示过滤后的结果
    for i, triple in enumerate(filtered_triples):
        print(f"  {i+1}. {triple['head']} ({triple['head_label']}) -> {triple['relation']} -> {triple['tail']} ({triple['tail_label']})")
        if triple.get('head_desc'):
            print(f"      头实体描述: {triple['head_desc']}")
        if triple.get('tail_desc'):
            print(f"      尾实体描述: {triple['tail_desc']}")
    
    # 4. 测试Neo4j连接
    print("\n4. 测试Neo4j连接...")
    try:
        driver = get_neo4j_driver()
        with driver.session() as session:
            result = session.run("RETURN 'Hello Neo4j!' as message")
            message = result.single()["message"]
            print(f"✓ Neo4j连接成功: {message}")
    except Exception as e:
        print(f"✗ Neo4j连接失败: {e}")
        return False
    
    # 5. 批量写入测试
    print("\n5. 批量写入测试...")
    try:
        df = pd.DataFrame(filtered_triples)
        batch_write_to_neo4j(driver, df)
        print("✓ 批量写入成功")
    except Exception as e:
        print(f"✗ 批量写入失败: {e}")
        driver.close()
        return False
    
    # 6. 验证写入结果
    print("\n6. 验证写入结果...")
    try:
        with driver.session() as session:
            # 统计节点和关系
            stats_query = """
            MATCH (n) 
            OPTIONAL MATCH (n)-[r]-()
            RETURN count(DISTINCT n) as nodes, count(DISTINCT r) as relationships
            """
            result = session.run(stats_query)
            stats = result.single()
            print(f"数据库中的节点: {stats['nodes']}, 关系: {stats['relationships']}")
            
            # 显示一些示例数据
            sample_query = """
            MATCH (n)-[r]->(m)
            RETURN n.name as head, labels(n)[0] as head_label, 
                   type(r) as relation, 
                   m.name as tail, labels(m)[0] as tail_label
            LIMIT 5
            """
            samples = list(session.run(sample_query))
            if samples:
                print("示例数据:")
                for sample in samples:
                    print(f"  {sample['head']} ({sample['head_label']}) -> {sample['relation']} -> {sample['tail']} ({sample['tail_label']})")
            
    except Exception as e:
        print(f"验证失败: {e}")
    finally:
        driver.close()
    
    print("\n✓ 测试完成！")
    return True

def clear_test_data():
    """清理测试数据"""
    try:
        driver = get_neo4j_driver()
        with driver.session() as session:
            # 删除所有节点和关系
            session.run("MATCH (n) DETACH DELETE n")
            print("✓ 测试数据已清理")
        driver.close()
    except Exception as e:
        print(f"清理失败: {e}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "clear":
        clear_test_data()
    elif len(sys.argv) > 1 and sys.argv[1] == "import":
        import_csv_data()
    else:
        test_small_pipeline()
