#!/usr/bin/env python3
"""
抽取到CSV脚本 - 不依赖Neo4j，先将抽取结果保存到CSV文件
"""

import os
import sys
import pandas as pd
from tqdm import tqdm
from dotenv import load_dotenv

from utils import (
    yield_chunks, load_whitelists, llm_extract, whitelist_filter
)

load_dotenv()

def extract_to_csv(input_file: str, output_csv: str = "extracted_triples.csv"):
    """
    从文本文件抽取实体关系并保存到CSV
    
    Args:
        input_file: 输入文本文件
        output_csv: 输出CSV文件
    """
    print(f"=== 抽取实体关系到CSV ===")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_csv}")
    
    # 检查文件
    if not os.path.exists(input_file):
        print(f"错误: 文件不存在 {input_file}")
        return False
    
    # 加载白名单
    print("\n加载白名单...")
    entities, labels, relations = load_whitelists()
    if not entities or not labels or not relations:
        print("错误: 白名单加载失败")
        return False
    
    print(f"实体白名单: {sorted(list(entities))}")
    print(f"标签白名单: {sorted(list(labels))}")
    print(f"关系白名单: {sorted(list(relations))}")
    
    # 读取文本
    try:
        with open(input_file, "r", encoding="utf-8") as f:
            text = f.read()
        print(f"\n文本长度: {len(text)} 字符")
    except Exception as e:
        print(f"错误: 读取文件失败 {e}")
        return False
    
    # 分块
    chunks = list(yield_chunks(text))
    print(f"分割成 {len(chunks)} 个文本块")
    
    # 处理每个块
    all_triples = []
    successful_chunks = 0
    
    print(f"\n开始抽取...")
    for i, chunk in enumerate(tqdm(chunks, desc="处理文本块")):
        try:
            # LLM抽取
            triples = llm_extract(chunk, entities, labels, relations)
            
            # 白名单过滤
            filtered_triples = list(whitelist_filter(triples, entities, labels, relations))
            
            if filtered_triples:
                all_triples.extend(filtered_triples)
                successful_chunks += 1
                tqdm.write(f"块 {i+1}: 抽取 {len(triples)} -> 过滤后 {len(filtered_triples)} 个三元组")
            else:
                tqdm.write(f"块 {i+1}: 没有有效的三元组")
                
        except Exception as e:
            tqdm.write(f"块 {i+1} 处理失败: {e}")
            continue
    
    # 保存结果
    if all_triples:
        try:
            df = pd.DataFrame(all_triples)
            df.to_csv(output_csv, index=False, encoding="utf-8")
            
            print(f"\n✓ 抽取完成!")
            print(f"成功处理: {successful_chunks}/{len(chunks)} 个文本块")
            print(f"总共抽取: {len(all_triples)} 个有效三元组")
            print(f"结果已保存到: {output_csv}")
            
            # 显示统计信息
            print(f"\n数据统计:")
            print(f"唯一头实体: {df['head'].nunique()}")
            print(f"唯一尾实体: {df['tail'].nunique()}")
            print(f"唯一关系: {df['relation'].nunique()}")
            
            # 显示前几个三元组
            print(f"\n前5个三元组:")
            for i, row in df.head().iterrows():
                print(f"  {i+1}. {row['head']} ({row['head_label']}) -> {row['relation']} -> {row['tail']} ({row['tail_label']})")
                if row.get('head_desc'):
                    print(f"      头实体: {row['head_desc']}")
                if row.get('tail_desc'):
                    print(f"      尾实体: {row['tail_desc']}")
            
            return True
            
        except Exception as e:
            print(f"保存CSV失败: {e}")
            return False
    else:
        print("没有抽取到任何有效的三元组")
        return False

def analyze_csv(csv_file: str):
    """分析CSV文件中的数据"""
    try:
        df = pd.read_csv(csv_file)
        print(f"=== 分析 {csv_file} ===")
        print(f"总三元组数: {len(df)}")
        
        # 按标签统计
        print(f"\n头实体标签分布:")
        head_labels = df['head_label'].value_counts()
        for label, count in head_labels.items():
            print(f"  {label}: {count}")
        
        print(f"\n尾实体标签分布:")
        tail_labels = df['tail_label'].value_counts()
        for label, count in tail_labels.items():
            print(f"  {label}: {count}")
        
        print(f"\n关系类型分布:")
        relations = df['relation'].value_counts()
        for relation, count in relations.items():
            print(f"  {relation}: {count}")
        
        # 检查数据质量
        print(f"\n数据质量检查:")
        missing_head_desc = df['head_desc'].isna().sum()
        missing_tail_desc = df['tail_desc'].isna().sum()
        print(f"缺少头实体描述: {missing_head_desc}")
        print(f"缺少尾实体描述: {missing_tail_desc}")
        
    except Exception as e:
        print(f"分析失败: {e}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python extract_to_csv.py <输入文件> [输出文件]")
        print("  python extract_to_csv.py analyze <CSV文件>")
        print("\n示例:")
        print("  python extract_to_csv.py python第八章.txt")
        print("  python extract_to_csv.py python第八章.txt my_output.csv")
        print("  python extract_to_csv.py analyze extracted_triples.csv")
        return
    
    if sys.argv[1] == "analyze":
        if len(sys.argv) < 3:
            print("请指定要分析的CSV文件")
            return
        analyze_csv(sys.argv[2])
    else:
        input_file = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) > 2 else "extracted_triples.csv"
        
        # 检查API key
        if not os.getenv("OPENAI_API_KEY"):
            print("错误: 未设置OPENAI_API_KEY环境变量")
            return
        
        extract_to_csv(input_file, output_file)

if __name__ == "__main__":
    main()
