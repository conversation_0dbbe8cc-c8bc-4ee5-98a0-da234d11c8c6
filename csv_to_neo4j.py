#!/usr/bin/env python3
"""
CSV到Neo4j导入脚本
将抽取的三元组CSV文件导入到Neo4j数据库
"""

import os
import sys
import pandas as pd
from dotenv import load_dotenv
from utils import get_neo4j_driver, batch_write_to_neo4j

load_dotenv()

def import_csv_to_neo4j(csv_file: str, clear_db: bool = False):
    """
    将CSV文件导入到Neo4j
    
    Args:
        csv_file: CSV文件路径
        clear_db: 是否先清空数据库
    """
    print(f"=== 导入CSV到Neo4j ===")
    print(f"CSV文件: {csv_file}")
    
    # 检查文件
    if not os.path.exists(csv_file):
        print(f"错误: 文件不存在 {csv_file}")
        return False
    
    # 读取CSV
    try:
        df = pd.read_csv(csv_file)
        print(f"读取到 {len(df)} 个三元组")
        
        # 检查必需的列
        required_cols = ['head', 'head_label', 'relation', 'tail', 'tail_label']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"错误: CSV缺少必需的列 {missing_cols}")
            return False
            
    except Exception as e:
        print(f"错误: 读取CSV失败 {e}")
        return False
    
    # 连接Neo4j
    try:
        driver = get_neo4j_driver()
        print("✓ 成功连接到Neo4j数据库")
    except Exception as e:
        print(f"错误: Neo4j连接失败 {e}")
        return False
    
    try:
        # 可选：清空数据库
        if clear_db:
            print("清空数据库...")
            with driver.session() as session:
                session.run("MATCH (n) DETACH DELETE n")
            print("✓ 数据库已清空")
        
        # 批量导入
        print("开始导入数据...")
        batch_write_to_neo4j(driver, df)
        
        # 验证导入结果
        print("\n验证导入结果...")
        with driver.session() as session:
            # 统计节点和关系
            stats_query = """
            MATCH (n) 
            OPTIONAL MATCH (n)-[r]-()
            RETURN count(DISTINCT n) as nodes, count(DISTINCT r) as relationships
            """
            result = session.run(stats_query)
            stats = result.single()
            print(f"数据库中的节点: {stats['nodes']}")
            print(f"数据库中的关系: {stats['relationships']}")
            
            # 检查约束违反
            missing_desc_query = """
            MATCH (n) 
            WHERE n.description IS NULL 
            RETURN count(n) as missing_count, collect(DISTINCT labels(n)) as labels
            """
            result = session.run(missing_desc_query)
            missing_result = result.single()
            missing_count = missing_result["missing_count"]
            
            if missing_count > 0:
                print(f"警告: {missing_count} 个节点缺少description属性")
                print(f"受影响的标签: {missing_result['labels']}")
            else:
                print("✓ 所有节点都有description属性")
            
            # 显示一些示例数据
            sample_query = """
            MATCH (n)-[r]->(m)
            RETURN n.name as head, labels(n)[0] as head_label, n.description as head_desc,
                   type(r) as relation, 
                   m.name as tail, labels(m)[0] as tail_label, m.description as tail_desc
            LIMIT 5
            """
            samples = list(session.run(sample_query))
            if samples:
                print(f"\n示例数据:")
                for i, sample in enumerate(samples, 1):
                    print(f"  {i}. {sample['head']} ({sample['head_label']}) -> {sample['relation']} -> {sample['tail']} ({sample['tail_label']})")
                    if sample['head_desc']:
                        print(f"     头实体: {sample['head_desc']}")
                    if sample['tail_desc']:
                        print(f"     尾实体: {sample['tail_desc']}")
        
        print("\n✓ 导入完成！")
        return True
        
    except Exception as e:
        print(f"导入失败: {e}")
        return False
    finally:
        driver.close()

def verify_database():
    """验证数据库状态"""
    try:
        driver = get_neo4j_driver()
        with driver.session() as session:
            print("=== 数据库验证 ===")
            
            # 基本统计
            stats_query = """
            MATCH (n) 
            OPTIONAL MATCH (n)-[r]-()
            RETURN count(DISTINCT n) as nodes, count(DISTINCT r) as relationships
            """
            result = session.run(stats_query)
            stats = result.single()
            print(f"节点总数: {stats['nodes']}")
            print(f"关系总数: {stats['relationships']}")
            
            # 按标签统计节点
            label_stats_query = """
            MATCH (n)
            RETURN labels(n)[0] as label, count(n) as count
            ORDER BY count DESC
            """
            label_stats = list(session.run(label_stats_query))
            print(f"\n节点标签分布:")
            for stat in label_stats:
                print(f"  {stat['label']}: {stat['count']}")
            
            # 按类型统计关系
            rel_stats_query = """
            MATCH ()-[r]->()
            RETURN type(r) as relation_type, count(r) as count
            ORDER BY count DESC
            """
            rel_stats = list(session.run(rel_stats_query))
            print(f"\n关系类型分布:")
            for stat in rel_stats:
                print(f"  {stat['relation_type']}: {stat['count']}")
            
            # 检查约束
            try:
                constraints_query = "SHOW CONSTRAINTS"
                constraints = list(session.run(constraints_query))
                print(f"\n约束数量: {len(constraints)}")
                for constraint in constraints[:5]:  # 只显示前5个
                    print(f"  - {constraint.get('name', 'N/A')}")
            except Exception:
                print("\n无法获取约束信息（可能是版本问题）")
        
        driver.close()
        
    except Exception as e:
        print(f"验证失败: {e}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python csv_to_neo4j.py <CSV文件> [--clear]")
        print("  python csv_to_neo4j.py verify")
        print("\n示例:")
        print("  python csv_to_neo4j.py extracted_triples.csv")
        print("  python csv_to_neo4j.py extracted_triples.csv --clear")
        print("  python csv_to_neo4j.py verify")
        return
    
    if sys.argv[1] == "verify":
        verify_database()
    else:
        csv_file = sys.argv[1]
        clear_db = "--clear" in sys.argv
        
        # 检查环境变量
        if not os.getenv("NEO4J_PASSWORD"):
            print("错误: 未设置NEO4J_PASSWORD环境变量")
            return
        
        import_csv_to_neo4j(csv_file, clear_db)

if __name__ == "__main__":
    main()
