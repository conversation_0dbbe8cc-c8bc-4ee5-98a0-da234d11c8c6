// Neo4j 约束创建脚本
// 为所有标签创建唯一性约束和存在性约束

// 删除现有约束（如果存在）
DROP CONSTRAINT concept_name_unique IF EXISTS;
DROP CONSTRAINT concept_desc_required IF EXISTS;
DROP CONSTRAINT situation_name_unique IF EXISTS;
DROP CONSTRAINT situation_desc_required IF EXISTS;
DROP CONSTRAINT function_name_unique IF EXISTS;
DROP CONSTRAINT function_desc_required IF EXISTS;
DROP CONSTRAINT parameter_name_unique IF EXISTS;
DROP CONSTRAINT parameter_desc_required IF EXISTS;
DROP CONSTRAINT datatype_name_unique IF EXISTS;
DROP CONSTRAINT datatype_desc_required IF EXISTS;
DROP CONSTRAINT codeblock_name_unique IF EXISTS;
DROP CONSTRAINT codeblock_desc_required IF EXISTS;
DROP CONSTRAINT variable_name_unique IF EXISTS;
DROP CONSTRAINT variable_desc_required IF EXISTS;
DROP CONSTRAINT method_name_unique IF EXISTS;
DROP CONSTRAINT method_desc_required IF EXISTS;
DROP CONSTRAINT class_name_unique IF EXISTS;
DROP CONSTRAINT class_desc_required IF EXISTS;
DROP CONSTRAINT object_name_unique IF EXISTS;
DROP CONSTRAINT object_desc_required IF EXISTS;

// 创建唯一性约束 - 确保每个标签下的name字段唯一
CREATE CONSTRAINT concept_name_unique IF NOT EXISTS
FOR (n:Concept) REQUIRE n.name IS UNIQUE;

CREATE CONSTRAINT situation_name_unique IF NOT EXISTS
FOR (n:Situation) REQUIRE n.name IS UNIQUE;

CREATE CONSTRAINT function_name_unique IF NOT EXISTS
FOR (n:Function) REQUIRE n.name IS UNIQUE;

CREATE CONSTRAINT parameter_name_unique IF NOT EXISTS
FOR (n:Parameter) REQUIRE n.name IS UNIQUE;

CREATE CONSTRAINT datatype_name_unique IF NOT EXISTS
FOR (n:DataType) REQUIRE n.name IS UNIQUE;

CREATE CONSTRAINT codeblock_name_unique IF NOT EXISTS
FOR (n:CodeBlock) REQUIRE n.name IS UNIQUE;

CREATE CONSTRAINT variable_name_unique IF NOT EXISTS
FOR (n:Variable) REQUIRE n.name IS UNIQUE;

CREATE CONSTRAINT method_name_unique IF NOT EXISTS
FOR (n:Method) REQUIRE n.name IS UNIQUE;

CREATE CONSTRAINT class_name_unique IF NOT EXISTS
FOR (n:Class) REQUIRE n.name IS UNIQUE;

CREATE CONSTRAINT object_name_unique IF NOT EXISTS
FOR (n:Object) REQUIRE n.name IS UNIQUE;

// 创建存在性约束 - 确保每个节点都有description字段
CREATE CONSTRAINT concept_desc_required IF NOT EXISTS
FOR (n:Concept) REQUIRE n.description IS NOT NULL;

CREATE CONSTRAINT situation_desc_required IF NOT EXISTS
FOR (n:Situation) REQUIRE n.description IS NOT NULL;

CREATE CONSTRAINT function_desc_required IF NOT EXISTS
FOR (n:Function) REQUIRE n.description IS NOT NULL;

CREATE CONSTRAINT parameter_desc_required IF NOT EXISTS
FOR (n:Parameter) REQUIRE n.description IS NOT NULL;

CREATE CONSTRAINT datatype_desc_required IF NOT EXISTS
FOR (n:DataType) REQUIRE n.description IS NOT NULL;

CREATE CONSTRAINT codeblock_desc_required IF NOT EXISTS
FOR (n:CodeBlock) REQUIRE n.description IS NOT NULL;

CREATE CONSTRAINT variable_desc_required IF NOT EXISTS
FOR (n:Variable) REQUIRE n.description IS NOT NULL;

CREATE CONSTRAINT method_desc_required IF NOT EXISTS
FOR (n:Method) REQUIRE n.description IS NOT NULL;

CREATE CONSTRAINT class_desc_required IF NOT EXISTS
FOR (n:Class) REQUIRE n.description IS NOT NULL;

CREATE CONSTRAINT object_desc_required IF NOT EXISTS
FOR (n:Object) REQUIRE n.description IS NOT NULL;

// 显示所有约束
SHOW CONSTRAINTS;
