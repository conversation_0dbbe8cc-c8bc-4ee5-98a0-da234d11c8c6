#!/usr/bin/env python3
"""
主管道脚本 - 从文本到Neo4j知识图谱的完整流程
使用GPT-4o抽取实体关系，并批量写入Neo4j数据库
"""

import os
import sys
import pandas as pd
from tqdm import tqdm
from dotenv import load_dotenv

# 导入自定义工具函数
from utils import (
    yield_chunks, load_whitelists, llm_extract, whitelist_filter,
    get_neo4j_driver, batch_write_to_neo4j
)

# 加载环境变量
load_dotenv()

def process_text_file(file_path: str, output_csv: str = None):
    """
    处理文本文件，抽取实体关系并写入Neo4j
    
    Args:
        file_path: 输入文本文件路径
        output_csv: 可选的CSV输出文件路径（用于调试）
    """
    print(f"开始处理文件: {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在 {file_path}")
        return False
    
    # 加载白名单
    print("加载白名单...")
    entities, labels, relations = load_whitelists()
    if not entities or not labels or not relations:
        print("错误: 白名单加载失败")
        return False
    
    # 读取文本文件
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            text = f.read()
        print(f"文本长度: {len(text)} 字符")
    except Exception as e:
        print(f"错误: 读取文件失败 {e}")
        return False
    
    # 分块处理
    chunks = list(yield_chunks(text))
    print(f"分割成 {len(chunks)} 个文本块")
    
    # 连接Neo4j数据库
    try:
        driver = get_neo4j_driver()
        print("✓ 成功连接到Neo4j数据库")
    except Exception as e:
        print(f"错误: Neo4j连接失败 {e}")
        return False
    
    # 处理每个文本块
    all_triples = []
    successful_chunks = 0
    
    print("\n开始抽取实体关系...")
    for i, chunk in enumerate(tqdm(chunks, desc="处理文本块")):
        try:
            # LLM抽取
            triples = llm_extract(chunk, entities, labels, relations)
            
            # 白名单过滤
            filtered_triples = list(whitelist_filter(triples, entities, labels, relations))
            
            if filtered_triples:
                # 转换为DataFrame
                df = pd.DataFrame(filtered_triples)
                
                # 批量写入Neo4j
                batch_write_to_neo4j(driver, df)
                
                # 保存到总列表（用于可选的CSV输出）
                all_triples.extend(filtered_triples)
                successful_chunks += 1
                
                tqdm.write(f"块 {i+1}: 抽取 {len(triples)} -> 过滤后 {len(filtered_triples)} 个三元组")
            else:
                tqdm.write(f"块 {i+1}: 没有有效的三元组")
                
        except Exception as e:
            tqdm.write(f"块 {i+1} 处理失败: {e}")
            continue
    
    # 关闭数据库连接
    driver.close()
    
    # 输出统计信息
    print(f"\n处理完成!")
    print(f"成功处理: {successful_chunks}/{len(chunks)} 个文本块")
    print(f"总共抽取: {len(all_triples)} 个有效三元组")
    
    # 可选：保存到CSV文件
    if output_csv and all_triples:
        try:
            df = pd.DataFrame(all_triples)
            df.to_csv(output_csv, index=False, encoding="utf-8")
            print(f"三元组已保存到: {output_csv}")
        except Exception as e:
            print(f"保存CSV失败: {e}")
    
    return True

def verify_database():
    """验证数据库中的数据"""
    try:
        driver = get_neo4j_driver()
        with driver.session() as session:
            # 统计节点和关系
            stats_query = """
            MATCH (n) 
            OPTIONAL MATCH (n)-[r]-()
            RETURN count(DISTINCT n) as nodes, count(DISTINCT r) as relationships
            """
            result = session.run(stats_query)
            stats = result.single()
            
            print(f"\n数据库统计:")
            print(f"节点数量: {stats['nodes']}")
            print(f"关系数量: {stats['relationships']}")
            
            # 检查约束
            constraints_query = "SHOW CONSTRAINTS"
            constraints = list(session.run(constraints_query))
            print(f"约束数量: {len(constraints)}")
            
            # 检查是否有节点缺少description
            missing_desc_query = """
            MATCH (n) 
            WHERE n.description IS NULL 
            RETURN count(n) as missing_count
            """
            result = session.run(missing_desc_query)
            missing = result.single()["missing_count"]
            
            if missing > 0:
                print(f"警告: {missing} 个节点缺少description属性")
            else:
                print("✓ 所有节点都有description属性")
                
        driver.close()
        return True
        
    except Exception as e:
        print(f"验证失败: {e}")
        return False

def main():
    """主函数"""
    print("=== Neo4j知识图谱构建管道 ===")
    
    # 检查环境变量
    required_vars = ["NEO4J_PASSWORD", "OPENAI_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"错误: 缺少环境变量 {missing_vars}")
        print("请检查.env文件")
        return
    
    # 默认输入文件
    input_file = "python第八章.txt"
    output_csv = "extracted_triples.csv"
    
    # 处理命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_csv = sys.argv[2]
    
    # 执行处理流程
    success = process_text_file(input_file, output_csv)
    
    if success:
        # 验证结果
        print("\n验证数据库...")
        verify_database()
        print("\n✓ 流程完成！")
    else:
        print("\n✗ 流程失败！")

if __name__ == "__main__":
    main()
