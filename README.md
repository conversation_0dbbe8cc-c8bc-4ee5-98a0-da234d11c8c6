# Neo4j知识图谱构建系统

基于GPT-4o和Neo4j的知识图谱构建系统，从Python编程教材中抽取实体关系并构建知识图谱。

## 功能特点

- ✅ **智能实体抽取**: 使用GPT-4o自动抽取实体关系
- ✅ **白名单过滤**: 严格按照预定义白名单过滤实体、标签和关系
- ✅ **自动描述生成**: LLM自动为每个实体生成中文描述
- ✅ **批量处理**: 支持大文本文件的分块处理
- ✅ **数据完整性**: Neo4j约束确保数据质量
- ✅ **灵活导入**: 支持CSV中转和直接Neo4j写入

## 项目结构

```
gyrw-kg/
├── whitelists/              # 白名单文件
│   ├── entities.csv         # 实体白名单（无header）
│   ├── labels.csv          # 标签白名单
│   └── relations.csv       # 关系白名单
├── utils.py                # 核心工具函数
├── pipeline.py             # 主管道脚本
├── extract_to_csv.py       # 抽取到CSV（推荐）
├── csv_to_neo4j.py         # CSV导入Neo4j
├── setup_constraints.py    # 约束设置
├── test_pipeline.py        # 测试脚本
├── cleaning.cql           # 数据清洗脚本
├── cy_constraints.cql     # 约束创建脚本
└── .env                   # 环境配置
```

## 快速开始

### 1. 环境准备

确保已安装：
- Neo4j 5.x + APOC插件
- Python 3.10+
- 所需Python包（见requirements.txt）

### 2. 配置环境变量

编辑`.env`文件：
```bash
# Neo4j配置
NEO4J_URI="bolt://localhost:7687"
NEO4J_USER="neo4j"
NEO4J_PASSWORD="your_password"

# OpenAI配置
OPENAI_API_KEY="sk-your-api-key"
```

### 3. 推荐流程（两步法）

#### 步骤1: 抽取到CSV
```bash
# 从文本抽取实体关系到CSV
python extract_to_csv.py python第八章.txt

# 分析抽取结果
python extract_to_csv.py analyze extracted_triples.csv
```

#### 步骤2: 导入到Neo4j
```bash
# 设置约束（首次运行）
python setup_constraints.py

# 导入CSV到Neo4j
python csv_to_neo4j.py extracted_triples.csv --clear

# 验证数据库
python csv_to_neo4j.py verify
```

### 4. 一步法（直接处理）

```bash
# 直接从文本到Neo4j（需要Neo4j连接正常）
python pipeline.py python第八章.txt
```

## 白名单配置

### entities.csv（无header）
```
函数
代码块
函数体
实参
形参
...
```

### labels.csv
```
label
Concept
Function
Parameter
CodeBlock
...
```

### relations.csv
```
relation_type
contains
depends_on
is_a_type_of
part_of
...
```

## 核心功能

### 文本分块
- 自动将大文本分割成约700 tokens的小块
- 保持语义完整性

### LLM抽取
- 使用GPT-4o-mini进行实体关系抽取
- JSON模式确保结构化输出
- 函数调用保证格式一致性

### 白名单过滤
- 只保留白名单中的实体
- 标签和关系严格受控
- LLM自动生成描述

### Neo4j集成
- 批量MERGE操作提高性能
- 约束确保数据完整性
- APOC支持大规模数据处理

## 数据质量保证

### 约束设置
- 每个标签的name字段唯一性约束
- 每个节点必须有description属性
- 自动验证数据完整性

### 清洗机制
- 使用APOC批量删除无效数据
- 白名单严格过滤
- 数据质量检查

## 示例输出

抽取的三元组示例：
```
函数 (Function) -> is_a_type_of -> 代码块 (CodeBlock)
  头实体描述: 带名字的代码块
  尾实体描述: 用于完成具体工作的代码块

形参 (Parameter) -> part_of -> 函数 (Function)
  头实体描述: 函数定义中需要的信息
  尾实体描述: 完成工作所需的信息
```

## 故障排除

### Neo4j连接问题
1. 确认Neo4j服务正在运行
2. 检查密码是否正确
3. 验证端口7687是否可访问
4. 使用CSV中转方式绕过连接问题

### API限制
- 使用gpt-4o-mini降低成本
- 实现重试机制
- 合理设置请求间隔

### 内存优化
- 流式处理大文件
- 批量写入减少内存占用
- 及时释放资源

## 扩展功能

### 自定义白名单
- 根据领域需求调整实体白名单
- 扩展标签和关系类型
- 支持多语言实体

### 批量处理
- 支持多文件批量处理
- 并行处理提高效率
- 进度跟踪和错误恢复

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
