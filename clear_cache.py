"""
清除密码缓存和重新加载配置
"""
import os
import sys
import importlib

def clear_environment_cache():
    """清除环境变量缓存"""
    print("🧹 清除环境变量缓存...")
    
    # 清除相关的环境变量
    env_vars = ['NEO4J_URI', 'NEO4J_USER', 'NEO4J_PASSWORD', 'NEO4J_DATABASE', 'OPENAI_API_KEY']
    
    for var in env_vars:
        if var in os.environ:
            del os.environ[var]
            print(f"✓ 清除 {var}")

def clear_module_cache():
    """清除Python模块缓存"""
    print("\n🧹 清除Python模块缓存...")
    
    modules_to_clear = ['config', 'dotenv']
    
    for module_name in modules_to_clear:
        if module_name in sys.modules:
            del sys.modules[module_name]
            print(f"✓ 清除模块 {module_name}")

def reload_config():
    """重新加载配置"""
    print("\n🔄 重新加载配置...")
    
    try:
        # 重新导入dotenv和config
        from dotenv import load_dotenv
        load_dotenv(override=True)  # 强制重新加载
        
        from config import Config
        Config.validate()
        
        print("✓ 配置重新加载成功")
        return True
        
    except Exception as e:
        print(f"❌ 配置重新加载失败: {e}")
        return False

def test_fresh_connection():
    """使用新配置测试连接"""
    print("\n🔍 使用新配置测试连接...")
    
    try:
        from neo4j import GraphDatabase
        from config import Config
        
        print(f"URI: {Config.NEO4J_URI}")
        print(f"用户: {Config.NEO4J_USER}")
        print(f"密码: {'*' * len(Config.NEO4J_PASSWORD) if Config.NEO4J_PASSWORD else 'None'}")
        
        driver = GraphDatabase.driver(
            Config.NEO4J_URI,
            auth=(Config.NEO4J_USER, Config.NEO4J_PASSWORD)
        )
        
        with driver.session(database=Config.NEO4J_DATABASE) as session:
            result = session.run("RETURN 'Hello Neo4j!' as message")
            record = result.single()
            print(f"✓ 连接成功: {record['message']}")
            
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧹 清除密码缓存并重新测试连接")
    print("=" * 60)
    
    # 清除缓存
    clear_environment_cache()
    clear_module_cache()
    
    # 重新加载配置
    config_ok = reload_config()
    
    if not config_ok:
        print("\n❌ 配置重新加载失败，请检查.env文件")
        return 1
    
    # 测试连接
    connection_ok = test_fresh_connection()
    
    if connection_ok:
        print("\n🎉 缓存清除成功，连接正常！")
        return 0
    else:
        print("\n⚠️ 缓存已清除，但连接仍有问题")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
