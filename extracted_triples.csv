head,head_label,head_desc,relation,tail,tail_label,tail_desc
函数,Concept,带名字的代码块,is_a_type_of,代码块,CodeBlock,用于完成具体工作的代码块
位置实参,Concept,实参与形参顺序相同的传递方式,is_a_type_of,实参,Parameter,在函数调用中传递的信息
关键字实参,Concept,通过变量名和对应值传递的实参,is_a_type_of,实参,Parameter,在函数调用中传递的信息
形参,Parameter,函数定义中需要的信息,part_of,函数,Function,完成工作所需的信息
实参,Parameter,在调用函数时传递的信息,part_of,函数,Function,完成工作所需的信息
函数,Function,用于执行特定任务的代码块,contains,形参,Parameter,函数接收的输入参数
函数,Function,用于执行特定任务的代码块,contains,返回值,Variable,函数执行后返回的结果
函数,Function,用于执行特定任务的代码块,requires,位置实参,Parameter,按位置传递给函数的参数
函数,Function,用于执行特定任务的代码块,requires,关键字实参,Parameter,以关键字方式传递给函数的参数
函数,Function,用于执行特定任务的代码块,part_of,代码块,CodeBlock,包含函数定义的代码段
代码块,CodeBlock,一段可执行的代码,contains,函数体,CodeBlock,函数的具体实现部分
函数,Function,用于执行特定任务的代码块,is_a_type_of,可选形参,Parameter,可以选择性传递给函数的参数
