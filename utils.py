#!/usr/bin/env python3
"""
工具函数模块
包含文本分块、LLM调用、过滤等功能
"""

import os
import json
import pandas as pd
from typing import List, Dict, Generator, Any
from openai import OpenAI
from neo4j import GraphDatabase
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def yield_chunks(text: str, max_chars: int = 2800) -> Generator[str, None, None]:
    """
    将文本分割成适合GPT处理的小块
    
    Args:
        text: 输入文本
        max_chars: 每块最大字符数（约700 tokens）
        
    Yields:
        str: 文本块
    """
    buffer = []
    current_size = 0
    
    for line in text.splitlines():
        line_length = len(line)
        
        # 如果当前行加上缓冲区会超过限制，先输出缓冲区
        if current_size + line_length > max_chars and buffer:
            yield "\n".join(buffer)
            buffer = []
            current_size = 0
        
        # 如果单行就超过限制，直接输出
        if line_length > max_chars:
            if buffer:
                yield "\n".join(buffer)
                buffer = []
                current_size = 0
            yield line
        else:
            buffer.append(line)
            current_size += line_length
    
    # 输出剩余内容
    if buffer:
        yield "\n".join(buffer)

def load_whitelists() -> tuple[set, set, set]:
    """
    加载白名单文件
    
    Returns:
        tuple: (实体白名单, 标签白名单, 关系白名单)
    """
    try:
        # 实体白名单（无header）
        entities_df = pd.read_csv("whitelists/entities.csv", header=None)
        entity_whitelist = set(entities_df[0].dropna().str.strip())
        
        # 标签白名单
        labels_df = pd.read_csv("whitelists/labels.csv")
        label_whitelist = set(labels_df["label"].dropna().str.strip())
        
        # 关系白名单
        relations_df = pd.read_csv("whitelists/relations.csv")
        relation_whitelist = set(relations_df["relation_type"].dropna().str.strip())
        
        print(f"加载白名单 - 实体: {len(entity_whitelist)}, 标签: {len(label_whitelist)}, 关系: {len(relation_whitelist)}")
        
        return entity_whitelist, label_whitelist, relation_whitelist
        
    except Exception as e:
        print(f"加载白名单失败: {e}")
        return set(), set(), set()

def get_openai_client() -> OpenAI:
    """
    创建OpenAI客户端
    
    Returns:
        OpenAI: 客户端实例
    """
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise ValueError("OPENAI_API_KEY环境变量未设置")
    
    return OpenAI(api_key=api_key)

def create_extraction_function_def() -> Dict[str, Any]:
    """
    创建GPT函数调用定义
    
    Returns:
        dict: 函数定义
    """
    return {
        "name": "add_triples",
        "description": "提取并返回实体关系三元组",
        "parameters": {
            "type": "object",
            "properties": {
                "triples": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "head": {"type": "string", "description": "头实体名称"},
                            "head_label": {"type": "string", "description": "头实体标签"},
                            "head_desc": {"type": "string", "description": "头实体描述"},
                            "relation": {"type": "string", "description": "关系类型"},
                            "tail": {"type": "string", "description": "尾实体名称"},
                            "tail_label": {"type": "string", "description": "尾实体标签"},
                            "tail_desc": {"type": "string", "description": "尾实体描述"}
                        },
                        "required": ["head", "head_label", "relation", "tail", "tail_label"]
                    }
                }
            },
            "required": ["triples"]
        }
    }

def create_system_prompt(entity_whitelist: set, label_whitelist: set, relation_whitelist: set) -> str:
    """
    创建系统提示词
    
    Args:
        entity_whitelist: 实体白名单
        label_whitelist: 标签白名单
        relation_whitelist: 关系白名单
        
    Returns:
        str: 系统提示词
    """
    return f"""你是一个信息抽取助手，专门从Python编程教材中抽取实体关系。

重要规则：
1. 只抽取在实体白名单中的实体：{sorted(list(entity_whitelist))}
2. 标签只能从以下列表选择：{sorted(list(label_whitelist))}
3. 关系只能从以下列表选择：{sorted(list(relation_whitelist))}
4. 如果实体不在白名单中，完全忽略它
5. 为每个保留的实体自动生成：
   - 从允许标签列表中选择合适的标签
   - 基于上下文生成简洁的中文描述（≤20字）

抽取重点：
- 函数相关概念（函数、参数、返回值等）
- 编程概念（代码块、变量、数据类型等）
- 概念之间的关系（包含、依赖、类型等）

请确保描述准确反映实体在Python编程中的含义。"""

def llm_extract(chunk: str, entity_whitelist: set, label_whitelist: set, relation_whitelist: set) -> List[Dict[str, str]]:
    """
    使用GPT-4o抽取实体关系三元组

    Args:
        chunk: 文本块
        entity_whitelist: 实体白名单
        label_whitelist: 标签白名单
        relation_whitelist: 关系白名单

    Returns:
        List[Dict]: 抽取的三元组列表
    """
    try:
        client = get_openai_client()
        function_def = create_extraction_function_def()
        system_prompt = create_system_prompt(entity_whitelist, label_whitelist, relation_whitelist)

        response = client.chat.completions.create(
            model="gpt-4o-mini",
            response_format={"type": "json_object"},
            temperature=0,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"请从以下文本中抽取实体关系，并以JSON格式返回：\n\n{chunk}"}
            ],
            functions=[function_def],
            function_call={"name": "add_triples"}
        )

        # 解析函数调用结果
        if response.choices[0].message.function_call:
            arguments = response.choices[0].message.function_call.arguments
            if isinstance(arguments, str):
                result = json.loads(arguments)
            else:
                result = arguments

            return result.get("triples", [])
        else:
            print("警告: GPT没有返回函数调用结果")
            return []

    except Exception as e:
        print(f"LLM抽取失败: {e}")
        return []

def whitelist_filter(triples: List[Dict[str, str]], entity_whitelist: set, label_whitelist: set, relation_whitelist: set) -> Generator[Dict[str, str], None, None]:
    """
    根据白名单过滤三元组

    Args:
        triples: 三元组列表
        entity_whitelist: 实体白名单
        label_whitelist: 标签白名单
        relation_whitelist: 关系白名单

    Yields:
        Dict: 过滤后的三元组
    """
    for triple in triples:
        # 检查必需字段
        required_fields = ["head", "head_label", "relation", "tail", "tail_label"]
        if not all(field in triple for field in required_fields):
            continue

        # 检查实体是否在白名单中
        if (triple["head"] in entity_whitelist and
            triple["tail"] in entity_whitelist and
            triple["head_label"] in label_whitelist and
            triple["tail_label"] in label_whitelist and
            triple["relation"] in relation_whitelist):

            # 确保描述字段存在
            if "head_desc" not in triple:
                triple["head_desc"] = ""
            if "tail_desc" not in triple:
                triple["tail_desc"] = ""

            yield triple

def get_neo4j_driver():
    """创建Neo4j驱动连接"""
    # 重新加载环境变量
    load_dotenv(override=True)

    # 强制使用正确的URI
    uri = "bolt://localhost:7687"
    user = os.getenv("NEO4J_USER", "neo4j")
    password = os.getenv("NEO4J_PASSWORD")

    if not password:
        raise ValueError("NEO4J_PASSWORD环境变量未设置")

    return GraphDatabase.driver(uri, auth=(user, password))

def batch_write_to_neo4j(driver, triples_df: pd.DataFrame):
    """
    批量写入三元组到Neo4j

    Args:
        driver: Neo4j驱动
        triples_df: 包含三元组的DataFrame
    """
    if triples_df.empty:
        print("没有数据需要写入")
        return

    with driver.session() as session:
        # 按照头标签、尾标签、关系类型分组，以便使用动态标签
        for (head_label, tail_label, relation), group in triples_df.groupby(
                ["head_label", "tail_label", "relation"]):

            try:
                # 构建动态Cypher查询
                query = f"""
                UNWIND $rows AS row
                MERGE (h:`{head_label}` {{name: row.head}})
                  ON CREATE SET h.description = row.head_desc
                  ON MATCH SET h.description = COALESCE(h.description, row.head_desc)
                MERGE (t:`{tail_label}` {{name: row.tail}})
                  ON CREATE SET t.description = row.tail_desc
                  ON MATCH SET t.description = COALESCE(t.description, row.tail_desc)
                MERGE (h)-[r:`{relation}`]->(t)
                """

                # 转换为记录格式
                records = group.to_dict("records")

                # 执行批量写入
                result = session.run(query, rows=records)
                result.consume()  # 确保查询执行完成

                print(f"✓ 写入 {len(records)} 个三元组: {head_label}-[{relation}]->{tail_label}")

            except Exception as e:
                print(f"✗ 写入失败 {head_label}-[{relation}]->{tail_label}: {e}")

def test_neo4j_connection():
    """测试Neo4j连接"""
    try:
        driver = get_neo4j_driver()
        with driver.session() as session:
            result = session.run("RETURN 'Hello Neo4j!' as message")
            message = result.single()["message"]
            print(f"✓ Neo4j连接成功: {message}")
        driver.close()
        return True
    except Exception as e:
        print(f"✗ Neo4j连接失败: {e}")
        return False

def test_text_chunking():
    """测试文本分块功能"""
    print("测试文本分块功能...")

    # 读取测试文件
    try:
        with open("python第八章.txt", "r", encoding="utf-8") as f:
            text = f.read()

        print(f"原文本长度: {len(text)} 字符")

        chunks = list(yield_chunks(text))
        print(f"分块数量: {len(chunks)}")

        for i, chunk in enumerate(chunks[:3]):  # 只显示前3块
            print(f"\n--- 块 {i+1} (长度: {len(chunk)}) ---")
            print(chunk[:200] + "..." if len(chunk) > 200 else chunk)

    except Exception as e:
        print(f"测试失败: {e}")

def test_llm_extraction():
    """测试LLM抽取功能"""
    print("\n测试LLM抽取功能...")

    # 加载白名单
    entities, labels, relations = load_whitelists()

    # 测试文本
    test_text = """
    函数是带名字的代码块，用于完成具体的工作。要执行函数定义的特定任务，可调用该函数。
    函数可以接受参数，参数是传递给函数的值。函数还可以返回值给调用者。
    """

    try:
        # 抽取三元组
        triples = llm_extract(test_text, entities, labels, relations)
        print(f"抽取到 {len(triples)} 个三元组")

        # 过滤三元组
        filtered_triples = list(whitelist_filter(triples, entities, labels, relations))
        print(f"过滤后剩余 {len(filtered_triples)} 个三元组")

        # 显示结果
        for i, triple in enumerate(filtered_triples[:3]):
            print(f"\n三元组 {i+1}:")
            print(f"  {triple['head']} ({triple['head_label']}) -> {triple['relation']} -> {triple['tail']} ({triple['tail_label']})")
            if triple.get('head_desc'):
                print(f"  头实体描述: {triple['head_desc']}")
            if triple.get('tail_desc'):
                print(f"  尾实体描述: {triple['tail_desc']}")

    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    # 测试功能
    test_text_chunking()

    # 测试白名单加载
    entities, labels, relations = load_whitelists()
    print(f"\n实体样例: {list(entities)[:5]}")
    print(f"标签样例: {list(labels)[:5]}")
    print(f"关系样例: {list(relations)[:5]}")

    # 测试Neo4j连接
    print("\n测试Neo4j连接...")
    neo4j_connected = test_neo4j_connection()

    # 测试LLM抽取（可选，需要API key）
    if os.getenv("OPENAI_API_KEY"):
        test_llm_extraction()

        # 如果Neo4j连接成功，测试批量写入
        if neo4j_connected:
            print("\n测试批量写入...")
            test_batch_write()
    else:
        print("\n跳过LLM测试（未设置OPENAI_API_KEY）")
