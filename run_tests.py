#!/usr/bin/env python3
"""
综合测试脚本 - 验证整个系统的功能
"""

import os
import sys
import subprocess
from dotenv import load_dotenv

load_dotenv()

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*50}")
    print(f"测试: {description}")
    print(f"命令: {cmd}")
    print('='*50)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✓ {description} - 成功")
            return True
        else:
            print(f"✗ {description} - 失败 (返回码: {result.returncode})")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"✗ {description} - 超时")
        return False
    except Exception as e:
        print(f"✗ {description} - 异常: {e}")
        return False

def check_prerequisites():
    """检查前置条件"""
    print("检查前置条件...")
    
    # 检查环境变量
    required_vars = ["OPENAI_API_KEY", "NEO4J_PASSWORD"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"✗ 缺少环境变量: {missing_vars}")
        return False
    
    # 检查文件
    required_files = [
        "python第八章.txt",
        "whitelists/entities.csv",
        "whitelists/labels.csv", 
        "whitelists/relations.csv"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"✗ 缺少文件: {missing_files}")
        return False
    
    print("✓ 前置条件检查通过")
    return True

def main():
    """主测试流程"""
    print("=== Neo4j知识图谱系统综合测试 ===")
    
    # 检查前置条件
    if not check_prerequisites():
        print("前置条件检查失败，退出测试")
        return
    
    test_results = []
    
    # 测试1: 工具函数测试
    success = run_command("python utils.py", "工具函数测试")
    test_results.append(("工具函数测试", success))
    
    # 测试2: 抽取到CSV
    success = run_command("python extract_to_csv.py python第八章.txt test_output.csv", "文本抽取到CSV")
    test_results.append(("文本抽取到CSV", success))
    
    # 测试3: 分析CSV结果
    if os.path.exists("test_output.csv"):
        success = run_command("python extract_to_csv.py analyze test_output.csv", "CSV结果分析")
        test_results.append(("CSV结果分析", success))
    
    # 测试4: Neo4j连接测试（可选）
    print(f"\n{'='*50}")
    print("测试: Neo4j连接")
    print('='*50)
    
    try:
        from utils import get_neo4j_driver
        driver = get_neo4j_driver()
        with driver.session() as session:
            result = session.run("RETURN 'Hello Neo4j!' as message")
            message = result.single()["message"]
            print(f"✓ Neo4j连接成功: {message}")
            neo4j_available = True
        driver.close()
    except Exception as e:
        print(f"✗ Neo4j连接失败: {e}")
        neo4j_available = False
    
    test_results.append(("Neo4j连接", neo4j_available))
    
    # 测试5: 约束设置（如果Neo4j可用）
    if neo4j_available:
        print("\nNeo4j可用，测试约束设置...")
        success = run_command("python setup_constraints.py", "约束设置")
        test_results.append(("约束设置", success))
        
        # 测试6: CSV导入Neo4j（如果有CSV文件）
        if os.path.exists("test_output.csv"):
            success = run_command("python csv_to_neo4j.py test_output.csv --clear", "CSV导入Neo4j")
            test_results.append(("CSV导入Neo4j", success))
            
            # 测试7: 数据库验证
            success = run_command("python csv_to_neo4j.py verify", "数据库验证")
            test_results.append(("数据库验证", success))
    else:
        print("Neo4j不可用，跳过数据库相关测试")
    
    # 输出测试总结
    print(f"\n{'='*60}")
    print("测试总结")
    print('='*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, success in test_results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name:<20} {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
    elif passed >= total * 0.7:
        print("⚠️  大部分测试通过，系统基本可用。")
    else:
        print("❌ 多个测试失败，请检查系统配置。")
    
    # 清理测试文件
    if os.path.exists("test_output.csv"):
        try:
            os.remove("test_output.csv")
            print("\n✓ 清理测试文件")
        except:
            print("\n⚠️  无法清理测试文件")

if __name__ == "__main__":
    main()
